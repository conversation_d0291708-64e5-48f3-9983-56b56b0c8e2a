/**
 * AI Service for planning operations
 * Simplified version for the refactored planning agent
 */

interface PlanningRequest {
  prompt: string
  isInteractive?: boolean
  answers?: Record<string, string>
  designStyleGuide?: any
  hasImages?: boolean
  userPreferences?: {
    model?: string
    apiKey?: string
    autonomousMode?: boolean
  }
}

interface StepRequest {
  step: string
  context: any
  answer?: string
}

class AIService {
  async generateInitialPlan(request: PlanningRequest) {
    // Simplified planning response
    return {
      projectType: this.detectProjectType(request.prompt),
      analysis: {
        complexity: "medium",
        estimatedTime: "2-4 weeks",
        mainFeatures: this.extractFeatures(request.prompt)
      },
      needsInput: false
    }
  }

  async processStep(request: StepRequest) {
    const { step, context } = request
    
    // Simplified step processing
    switch (step) {
      case 'analyze':
        return this.analyzeProject(context)
      case 'clarify':
        return this.clarifyRequirements(context)
      case 'summary':
        return this.generateSummary(context)
      case 'techstack':
        return this.selectTechStack(context)
      case 'prd':
        return this.createPRD(context)
      case 'wireframes':
        return this.designWireframes(context)
      case 'design':
        return this.createDesignGuidelines(context)
      case 'filesystem':
        return this.planFileStructure(context)
      case 'workflow':
        return this.defineWorkflow(context)
      case 'tasks':
        return this.breakdownTasks(context)
      case 'scaffold':
        return this.generateScaffold(context)
      default:
        return { message: `Step ${step} completed`, data: {} }
    }
  }

  async analyzePrompt(prompt: string, context?: any, aiConfig?: any) {
    // Analyze the user's prompt and extract key information
    const projectType = this.detectProjectType(prompt)
    const features = this.extractFeatures(prompt)

    console.log("🔍 Analyzing prompt:", prompt.substring(0, 100) + "...")
    console.log("🎯 Detected project type:", projectType)
    console.log("⚡ Extracted features:", features)

    return {
      projectType,
      features,
      complexity: this.assessComplexity(prompt),
      estimatedTime: this.estimateTime(prompt),
      recommendations: this.generateRecommendations(prompt),
      analysis: {
        mainGoal: this.extractMainGoal(prompt),
        targetAudience: this.identifyTargetAudience(prompt),
        keyRequirements: features
      },
      // Include context information if provided
      ...(context?.designStyleGuide && { designStyleGuide: context.designStyleGuide }),
      ...(context?.hasImages && { hasImages: context.hasImages })
    }
  }

  async generateClarificationQuestions(prompt: string, analysis: any) {
    // Generate clarification questions based on the prompt and analysis
    const questions = []

    // Standard questions for crypto assistant projects
    if (analysis.projectType === 'ai agent' || prompt.toLowerCase().includes('crypto')) {
      questions.push({
        id: 'target_users',
        question: 'Who are the primary users of this crypto assistant? (e.g., retail traders, institutional investors, DeFi users)',
        type: 'text',
        required: true
      })

      questions.push({
        id: 'platform',
        question: 'What platforms should this run on? (e.g., Web app, Discord bot, Telegram bot, API service)',
        type: 'text',
        required: true
      })

      questions.push({
        id: 'timeline',
        question: 'What is your target timeline for this project? (e.g., 2 weeks MVP, 1 month full version)',
        type: 'text',
        required: true
      })

      questions.push({
        id: 'budget',
        question: 'Do you have any budget constraints for API costs? (e.g., free tier only, $100/month, unlimited)',
        type: 'text',
        required: false
      })
    } else {
      // Generic questions for other project types
      questions.push({
        id: 'target_users',
        question: 'Who are the primary users of this application?',
        type: 'text',
        required: true
      })

      questions.push({
        id: 'platform',
        question: 'What platform should this run on? (e.g., Web, Mobile, Desktop)',
        type: 'text',
        required: true
      })
    }

    return {
      questions,
      totalQuestions: questions.length,
      purpose: 'Gather additional details to create a more accurate development plan'
    }
  }

  async generateSummary(prompt: string, analysis: any, clarifications: any) {
    // Generate a comprehensive project summary
    return {
      overview: `${analysis.projectType} focused on ${analysis.analysis?.mainGoal || 'core functionality'}`,
      objectives: [
        'Build a functional and reliable solution',
        'Ensure good user experience and performance',
        'Implement proper security and error handling',
        'Create maintainable and scalable code'
      ],
      scope: `${analysis.complexity} complexity project with estimated timeline of ${analysis.estimatedTime}`,
      keyFeatures: analysis.features || ['Core functionality'],
      targetAudience: analysis.analysis?.targetAudience || 'General users',
      successCriteria: [
        'All core features working as expected',
        'Positive user feedback and adoption',
        'Stable performance under normal load',
        'Successful deployment and maintenance'
      ]
    }
  }

  async selectTechStack(prompt: string, analysis: any, clarifications: any) {
    // Select appropriate technology stack based on project requirements
    const lowerPrompt = prompt.toLowerCase()

    let frontend = ['React', 'TypeScript', 'Tailwind CSS']
    let backend = ['Node.js', 'Express']
    let database = ['PostgreSQL']
    let tools = ['Git', 'Docker', 'Vercel']
    let apis = []

    // Customize based on project type
    if (lowerPrompt.includes('crypto') || lowerPrompt.includes('blockchain')) {
      apis.push('Moralis Web3 API', 'CoinGecko API', 'CoinMarketCap API')
      backend.push('Web3.js', 'Ethers.js')
    }

    if (lowerPrompt.includes('agent') || lowerPrompt.includes('ai')) {
      backend.push('LangChain', 'LangGraph')
      apis.push('OpenAI API', 'Anthropic API')
    }

    if (lowerPrompt.includes('mcp')) {
      backend.push('MCP Protocol', 'Sequential MCP')
    }

    if (lowerPrompt.includes('mobile')) {
      frontend = ['React Native', 'TypeScript', 'NativeWind']
    }

    return {
      frontend,
      backend,
      database,
      apis,
      tools,
      reasoning: `Selected based on ${analysis.projectType} requirements and ${analysis.complexity} complexity`,
      alternatives: {
        frontend: lowerPrompt.includes('mobile') ? ['Flutter', 'Swift/Kotlin'] : ['Vue.js', 'Svelte'],
        backend: ['Python/FastAPI', 'Go/Gin', 'Rust/Axum'],
        database: ['MongoDB', 'Redis', 'SQLite']
      }
    }
  }

  async createPRD(prompt: string, analysis: any, techStack: any, clarifications: any) {
    // Create a Product Requirements Document
    return {
      title: `${analysis.projectType} - Product Requirements Document`,
      version: '1.0',
      overview: analysis.analysis?.mainGoal || 'Build a functional application',
      objectives: [
        'Deliver core functionality as specified',
        'Ensure good user experience',
        'Maintain code quality and security',
        'Enable future scalability'
      ],
      features: analysis.features?.map((feature: string, index: number) => ({
        id: `F${index + 1}`,
        name: feature,
        priority: index < 3 ? 'High' : 'Medium',
        description: `Implement ${feature.toLowerCase()} functionality`,
        acceptanceCriteria: [
          'Feature works as expected',
          'Proper error handling',
          'Good performance',
          'User-friendly interface'
        ]
      })) || [],
      technicalRequirements: {
        performance: 'Response time < 2 seconds for most operations',
        security: 'Implement proper authentication and data validation',
        scalability: 'Support for expected user load',
        compatibility: 'Works on modern browsers/devices'
      },
      timeline: analysis.estimatedTime,
      risks: [
        'API rate limits and costs',
        'Third-party service dependencies',
        'Complexity of integration',
        'User adoption challenges'
      ]
    }
  }

  async answerQuestionsAutonomously(prompt: string, analysis: any, questions: any[]) {
    // Answer questions autonomously based on the prompt and analysis
    const answers: Record<string, string> = {}

    for (const question of questions) {
      let answer = ""

      switch (question.id) {
        case 'target_users':
          if (prompt.toLowerCase().includes('crypto') || prompt.toLowerCase().includes('trading')) {
            answer = "Retail crypto traders and DeFi users who need real-time market analysis and investment guidance"
          } else {
            answer = "General users interested in the application's core functionality"
          }
          break

        case 'platform':
          if (prompt.toLowerCase().includes('agent') || prompt.toLowerCase().includes('assistant')) {
            answer = "Web application with API endpoints, potentially expandable to Discord/Telegram bots"
          } else {
            answer = "Web application"
          }
          break

        case 'timeline':
          const complexity = analysis?.complexity || 'medium'
          if (complexity === 'high') {
            answer = "4-6 weeks for MVP, 8-12 weeks for full version"
          } else if (complexity === 'low') {
            answer = "1-2 weeks for MVP, 3-4 weeks for full version"
          } else {
            answer = "2-4 weeks for MVP, 6-8 weeks for full version"
          }
          break

        case 'budget':
          if (prompt.toLowerCase().includes('crypto') || prompt.toLowerCase().includes('api')) {
            answer = "$200-500/month for API costs (Moralis, OpenAI, market data)"
          } else {
            answer = "$50-100/month for basic hosting and API costs"
          }
          break

        default:
          answer = "Standard implementation following industry best practices"
      }

      answers[question.id] = answer
    }

    return {
      answers,
      reasoning: "Generated based on project analysis and industry best practices",
      confidence: "high",
      assumptions: [
        "Standard user expectations for this type of application",
        "Typical development timeline for similar projects",
        "Industry-standard API costs and hosting requirements"
      ]
    }
  }

  async generateAutonomousAnswer(request: any) {
    // Simplified autonomous answer generation for single questions
    const { prompt, analysis, question } = request

    if (question) {
      const result = await this.answerQuestionsAutonomously(prompt, analysis, [question])
      return {
        answer: result.answers[question.id] || "Proceeding with recommended approach based on best practices",
        reasoning: result.reasoning,
        confidence: result.confidence
      }
    }

    return {
      answer: "Proceeding with recommended approach based on best practices",
      reasoning: "Standard implementation approach",
      confidence: "medium"
    }
  }

  private detectProjectType(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase()
    
    if (lowerPrompt.includes('web app') || lowerPrompt.includes('website')) {
      return 'web application'
    }
    if (lowerPrompt.includes('mobile app') || lowerPrompt.includes('ios') || lowerPrompt.includes('android')) {
      return 'mobile application'
    }
    if (lowerPrompt.includes('api') || lowerPrompt.includes('backend')) {
      return 'api service'
    }
    if (lowerPrompt.includes('agent') || lowerPrompt.includes('bot') || lowerPrompt.includes('ai')) {
      return 'ai agent'
    }
    
    return 'application'
  }

  private extractFeatures(prompt: string): string[] {
    // Enhanced feature extraction for crypto assistant
    const features = []
    const lowerPrompt = prompt.toLowerCase()

    if (lowerPrompt.includes('token') || lowerPrompt.includes('crypto')) {
      features.push('Token research and analysis')
    }
    if (lowerPrompt.includes('wallet')) {
      features.push('Wallet analysis')
    }
    if (lowerPrompt.includes('chain') || lowerPrompt.includes('blockchain')) {
      features.push('Multi-chain support')
    }
    if (lowerPrompt.includes('news')) {
      features.push('News aggregation')
    }
    if (lowerPrompt.includes('technical analysis') || lowerPrompt.includes('ta')) {
      features.push('Technical analysis')
    }
    if (lowerPrompt.includes('investing') || lowerPrompt.includes('advice')) {
      features.push('Investment advice')
    }
    if (lowerPrompt.includes('agent') || lowerPrompt.includes('ai')) {
      features.push('AI agent capabilities')
    }
    if (lowerPrompt.includes('moralis')) {
      features.push('Moralis integration')
    }
    if (lowerPrompt.includes('langgraph')) {
      features.push('LangGraph workflow')
    }
    if (lowerPrompt.includes('mcp')) {
      features.push('MCP protocol integration')
    }

    // Fallback features
    if (lowerPrompt.includes('user') || lowerPrompt.includes('login')) {
      features.push('User authentication')
    }
    if (lowerPrompt.includes('database') || lowerPrompt.includes('data')) {
      features.push('Data management')
    }
    if (lowerPrompt.includes('api')) {
      features.push('API integration')
    }
    if (lowerPrompt.includes('ui') || lowerPrompt.includes('interface')) {
      features.push('User interface')
    }

    return features.length > 0 ? features : ['Core functionality']
  }

  private assessComplexity(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase()
    let complexityScore = 0

    // Increase complexity based on features
    if (lowerPrompt.includes('multi') || lowerPrompt.includes('several')) complexityScore += 2
    if (lowerPrompt.includes('agent') || lowerPrompt.includes('ai')) complexityScore += 2
    if (lowerPrompt.includes('analysis') || lowerPrompt.includes('research')) complexityScore += 1
    if (lowerPrompt.includes('chain') || lowerPrompt.includes('blockchain')) complexityScore += 2
    if (lowerPrompt.includes('real-time') || lowerPrompt.includes('live')) complexityScore += 1

    if (complexityScore >= 5) return 'high'
    if (complexityScore >= 3) return 'medium'
    return 'low'
  }

  private estimateTime(prompt: string): string {
    const complexity = this.assessComplexity(prompt)
    const lowerPrompt = prompt.toLowerCase()

    let baseWeeks = 2

    if (lowerPrompt.includes('agent') || lowerPrompt.includes('ai')) baseWeeks += 2
    if (lowerPrompt.includes('multi') || lowerPrompt.includes('several')) baseWeeks += 1
    if (lowerPrompt.includes('analysis') || lowerPrompt.includes('research')) baseWeeks += 1

    switch (complexity) {
      case 'high': return `${baseWeeks + 2}-${baseWeeks + 4} weeks`
      case 'medium': return `${baseWeeks}-${baseWeeks + 2} weeks`
      default: return `${baseWeeks}-${baseWeeks + 1} weeks`
    }
  }

  private generateRecommendations(prompt: string): string[] {
    const recommendations = []
    const lowerPrompt = prompt.toLowerCase()

    if (lowerPrompt.includes('crypto') || lowerPrompt.includes('token')) {
      recommendations.push('Use established crypto APIs like CoinGecko or CoinMarketCap')
      recommendations.push('Implement proper rate limiting for API calls')
    }

    if (lowerPrompt.includes('agent') || lowerPrompt.includes('ai')) {
      recommendations.push('Use LangChain/LangGraph for agent orchestration')
      recommendations.push('Implement proper memory management for conversations')
    }

    if (lowerPrompt.includes('moralis')) {
      recommendations.push('Leverage Moralis Web3 APIs for blockchain data')
      recommendations.push('Use Moralis authentication for wallet connections')
    }

    if (lowerPrompt.includes('multi') || lowerPrompt.includes('several')) {
      recommendations.push('Design modular architecture for scalability')
      recommendations.push('Implement proper error handling across services')
    }

    recommendations.push('Follow security best practices for financial applications')
    recommendations.push('Implement comprehensive testing strategy')

    return recommendations
  }

  private extractMainGoal(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase()

    if (lowerPrompt.includes('crypto assistant')) {
      return 'Build an AI-powered crypto assistant for research and analysis'
    }
    if (lowerPrompt.includes('agent')) {
      return 'Create an intelligent agent system'
    }

    return 'Develop a functional application based on requirements'
  }

  private identifyTargetAudience(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase()

    if (lowerPrompt.includes('crypto') || lowerPrompt.includes('investing')) {
      return 'Crypto traders and investors'
    }
    if (lowerPrompt.includes('research')) {
      return 'Researchers and analysts'
    }

    return 'General users'
  }

  private analyzeProject(context: any) {
    return {
      projectType: context.projectType || 'application',
      requirements: ['User interface', 'Core functionality', 'Data management'],
      goals: ['Build functional application', 'Ensure good user experience'],
      scope: 'Medium complexity project with standard features'
    }
  }

  private clarifyRequirements(context: any) {
    return {
      clarifications: ['Requirements clarified based on initial analysis'],
      refinedScope: 'Scope refined for optimal development approach'
    }
  }

  private generateSummary(context: any) {
    return {
      overview: 'Project summary generated based on analysis and requirements',
      objectives: ['Deliver functional solution', 'Meet user needs', 'Ensure maintainability']
    }
  }

  private selectTechStack(context: any) {
    return {
      frontend: ['React', 'TypeScript', 'Tailwind CSS'],
      backend: ['Node.js', 'Express', 'PostgreSQL'],
      tools: ['Git', 'Docker', 'Vercel']
    }
  }

  private createPRD(context: any) {
    return {
      document: 'Product Requirements Document created with detailed specifications',
      features: ['Core features defined', 'User stories documented', 'Acceptance criteria established']
    }
  }

  private designWireframes(context: any) {
    return {
      wireframes: 'UI wireframes designed for optimal user experience',
      layouts: ['Main layout', 'Component layouts', 'Responsive design']
    }
  }

  private createDesignGuidelines(context: any) {
    return {
      theme: context.designStyleGuide || {
        colors: { primary: '#ff2d55', secondary: '#0a0a0a' },
        typography: 'Modern sans-serif',
        spacing: 'Consistent spacing system'
      },
      components: 'Design system components defined'
    }
  }

  private planFileStructure(context: any) {
    return {
      structure: {
        'src/': ['components/', 'pages/', 'utils/', 'styles/'],
        'public/': ['images/', 'icons/'],
        'docs/': ['README.md', 'API.md']
      }
    }
  }

  private defineWorkflow(context: any) {
    return {
      workflow: 'Development workflow defined with clear processes',
      stages: ['Planning', 'Development', 'Testing', 'Deployment']
    }
  }

  private breakdownTasks(context: any) {
    return {
      tasks: [
        { name: 'Setup project structure', priority: 'high', estimate: '1 day' },
        { name: 'Implement core features', priority: 'high', estimate: '1 week' },
        { name: 'Add styling and UI', priority: 'medium', estimate: '3 days' },
        { name: 'Testing and deployment', priority: 'medium', estimate: '2 days' }
      ]
    }
  }

  private generateScaffold(context: any) {
    return {
      scaffold: 'Project scaffold generated with initial structure and configuration',
      files: ['package.json', 'tsconfig.json', 'tailwind.config.js', 'README.md']
    }
  }

  // Additional methods needed by the API
  async generateContextProfile(prompt: string, analysis: any, summary: any, prd: any) {
    return {
      projectContext: `${analysis.projectType} with ${analysis.complexity} complexity`,
      userStories: prd.features?.slice(0, 5).map((f: any) => `As a user, I want ${f.name.toLowerCase()}`) || [],
      technicalContext: summary.scope,
      businessContext: summary.overview,
      constraints: ['Time', 'Budget', 'Technical complexity'],
      assumptions: ['Users have basic technical knowledge', 'Stable internet connection required']
    }
  }

  async designWireframes(prompt: string, analysis: any, prd: any) {
    return {
      wireframes: [
        { name: 'Landing Page', description: 'Main entry point with key features' },
        { name: 'Dashboard', description: 'Main user interface after login' },
        { name: 'Settings', description: 'User preferences and configuration' }
      ],
      userFlow: ['Landing → Registration → Dashboard → Core Features'],
      components: ['Header', 'Navigation', 'Content Area', 'Footer'],
      responsive: true
    }
  }

  async planFileSystem(prompt: string, techStack: any, analysis: any, designContext?: any) {
    const isReactProject = techStack.frontend?.includes('React')
    const isNodeProject = techStack.backend?.includes('Node.js')

    return {
      structure: {
        'src/': isReactProject ? ['components/', 'pages/', 'hooks/', 'utils/', 'styles/'] : ['lib/', 'utils/', 'types/'],
        'public/': ['images/', 'icons/', 'favicon.ico'],
        'docs/': ['README.md', 'API.md', 'DEPLOYMENT.md'],
        ...(isNodeProject && { 'api/': ['routes/', 'middleware/', 'controllers/'] })
      },
      keyFiles: [
        'package.json',
        'tsconfig.json',
        isReactProject ? 'next.config.js' : 'server.js',
        '.env.example',
        '.gitignore'
      ]
    }
  }

  async defineWorkflow(prompt: string, analysis: any, prd: any, wireframes: any, designContext?: any) {
    return {
      developmentPhases: [
        { phase: 'Setup', duration: '1-2 days', tasks: ['Project initialization', 'Environment setup'] },
        { phase: 'Core Development', duration: '1-2 weeks', tasks: ['Implement main features', 'API integration'] },
        { phase: 'Testing & Polish', duration: '3-5 days', tasks: ['Testing', 'Bug fixes', 'UI polish'] },
        { phase: 'Deployment', duration: '1-2 days', tasks: ['Production setup', 'Go live'] }
      ],
      gitWorkflow: 'Feature branches with PR reviews',
      testingStrategy: 'Unit tests + Integration tests',
      deploymentStrategy: 'Continuous deployment with staging environment'
    }
  }

  async designDatabaseSchema(prompt: string, analysis: any, prd: any, techStack: any) {
    const needsUsers = prompt.toLowerCase().includes('user') || prompt.toLowerCase().includes('auth')
    const isCrypto = prompt.toLowerCase().includes('crypto') || prompt.toLowerCase().includes('token')

    const tables = []

    if (needsUsers) {
      tables.push({
        name: 'users',
        fields: ['id', 'email', 'password_hash', 'created_at', 'updated_at']
      })
    }

    if (isCrypto) {
      tables.push({
        name: 'portfolios',
        fields: ['id', 'user_id', 'name', 'created_at']
      }, {
        name: 'holdings',
        fields: ['id', 'portfolio_id', 'token_address', 'amount', 'updated_at']
      })
    }

    return {
      tables,
      relationships: needsUsers ? ['users -> portfolios (1:many)', 'portfolios -> holdings (1:many)'] : [],
      indexes: ['email (unique)', 'user_id', 'token_address'],
      migrations: 'Sequential migration files for schema changes'
    }
  }

  async generateProjectScaffold(prompt: string, analysis: any, techStack: any, filesystem: any, database: any) {
    return {
      projectStructure: filesystem.structure,
      packageJson: {
        name: analysis.projectType.replace(/\s+/g, '-').toLowerCase(),
        version: '1.0.0',
        dependencies: this.generateDependencies(techStack),
        scripts: this.generateScripts(techStack)
      },
      environmentVariables: this.generateEnvVars(analysis, techStack),
      initialFiles: [
        'README.md with setup instructions',
        'Basic configuration files',
        'Initial component/page structure',
        'Database migration files'
      ],
      nextSteps: [
        'Run npm install',
        'Set up environment variables',
        'Initialize database',
        'Start development server'
      ]
    }
  }

  private generateDependencies(techStack: any) {
    const deps: Record<string, string> = {}

    if (techStack.frontend?.includes('React')) {
      deps['react'] = '^18.0.0'
      deps['react-dom'] = '^18.0.0'
      deps['next'] = '^14.0.0'
    }

    if (techStack.frontend?.includes('TypeScript')) {
      deps['typescript'] = '^5.0.0'
      deps['@types/react'] = '^18.0.0'
    }

    if (techStack.backend?.includes('Express')) {
      deps['express'] = '^4.18.0'
    }

    return deps
  }

  private generateScripts(techStack: any) {
    const scripts: Record<string, string> = {
      'dev': 'next dev',
      'build': 'next build',
      'start': 'next start',
      'lint': 'next lint'
    }

    if (techStack.backend?.includes('Node.js')) {
      scripts['server'] = 'node server.js'
    }

    return scripts
  }

  private generateEnvVars(analysis: any, techStack: any) {
    const vars = ['NODE_ENV=development']

    if (techStack.apis?.includes('OpenAI API')) {
      vars.push('OPENAI_API_KEY=your_openai_key_here')
    }

    if (techStack.apis?.includes('Moralis Web3 API')) {
      vars.push('MORALIS_API_KEY=your_moralis_key_here')
    }

    if (techStack.database?.includes('PostgreSQL')) {
      vars.push('DATABASE_URL=postgresql://user:password@localhost:5432/dbname')
    }

    return vars
  }
}

export const aiService = new AIService()
