import { useState, useCallback } from "react"
import type { 
  PlanningState, 
  PlanningActions, 
  PlanningTask, 
  Question, 
  ChatMessage 
} from "@/types/planning"
import { getBasePlanningTasks } from "@/lib/planning-constants"

/**
 * Custom hook for managing planning state
 */
export function usePlanningState(): PlanningState & PlanningActions {
  // Core planning state
  const [userPrompt, setUserPrompt] = useState("")
  const [hasStarted, setHasStarted] = useState(false)
  const [isInteractive, setIsInteractive] = useState(false)
  const [tasks, setTasks] = useState<PlanningTask[]>(getBasePlanningTasks())
  const [currentTaskIndex, setCurrentTaskIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<Record<string, any>>({})
  const [showResults, setShowResults] = useState(false)

  // Question handling
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [questionAnswer, setQuestionAnswer] = useState("")
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({})
  const [planningContext, setPlanningContext] = useState<any>(null)

  // Error handling
  const [error, setError] = useState<string | null>(null)
  const [canRetry, setCanRetry] = useState(false)

  // Image handling
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [isProcessingImages, setIsProcessingImages] = useState(false)
  const [designStyleGuideState, setDesignStyleGuideState] = useState<string | null>(null)

  // Settings
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [userApiKey, setUserApiKey] = useState("")
  const [preferredModel, setPreferredModel] = useState("anthropic/claude-sonnet-4")
  const [isAutonomousMode, setIsAutonomousMode] = useState(true)

  // UI state
  const [activeTab, setActiveTab] = useState<"preview" | "code" | "planning" | "graph">("preview")
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [chatInput, setChatInput] = useState("")
  const [sidebarWidth, setSidebarWidth] = useState(500)
  const [isResizing, setIsResizing] = useState(false)
  const [selectedPlanningSection, setSelectedPlanningSection] = useState<string | null>(null)

  // Enhanced setters with validation and side effects
  const setUserAnswersEnhanced = useCallback((answers: Record<string, string> | ((prev: Record<string, string>) => Record<string, string>)) => {
    if (typeof answers === 'function') {
      setUserAnswers(answers)
    } else {
      setUserAnswers(answers)
    }
    // Could add validation or persistence here
  }, [])

  const setResultsEnhanced = useCallback((newResults: Record<string, any> | ((prev: Record<string, any>) => Record<string, any>)) => {
    if (typeof newResults === 'function') {
      setResults(newResults)
    } else {
      setResults(newResults)
    }
    // Don't automatically show results view - let user stay in main interface
  }, [])

  const setCurrentTaskIndexEnhanced = useCallback((index: number) => {
    console.log(`Setting currentTaskIndex to: ${index}`)
    setCurrentTaskIndex(index)
  }, [])

  const addChatMessage = useCallback((message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date()
    }
    setChatMessages(prev => [...prev, newMessage])
  }, [])

  const setUploadedImagesEnhanced = useCallback((images: File[] | ((prev: File[]) => File[])) => {
    if (typeof images === 'function') {
      setUploadedImages(images)
    } else {
      setUploadedImages(images)
    }
  }, [])

  const removeUploadedImage = useCallback((index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }, [])

  const clearAllImages = useCallback(() => {
    setUploadedImages([])
  }, [])

  const resetPlanningState = useCallback(() => {
    setUserPrompt("")
    setHasStarted(false)
    setIsInteractive(false)
    setTasks(getBasePlanningTasks())
    setCurrentTaskIndex(-1)
    setIsProcessing(false)
    setResults({})
    setShowResults(false)
    setCurrentQuestion(null)
    setQuestionAnswer("")
    setUserAnswers({})
    setPlanningContext(null)
    setError(null)
    setCanRetry(false)
    setUploadedImages([])
    setIsProcessingImages(false)
    setDesignStyleGuideState(null)
    setChatMessages([])
    setSelectedPlanningSection(null)
  }, [])

  return {
    // State
    userPrompt,
    hasStarted,
    isInteractive,
    tasks,
    currentTaskIndex,
    isProcessing,
    results,
    showResults,
    currentQuestion,
    questionAnswer,
    userAnswers,
    planningContext,
    error,
    canRetry,
    uploadedImages,
    isProcessingImages,
    designStyleGuideState,
    isSettingsOpen,
    userApiKey,
    preferredModel,
    isAutonomousMode,
    activeTab,
    chatMessages,
    chatInput,
    sidebarWidth,
    isResizing,
    selectedPlanningSection,

    // Actions
    setUserPrompt,
    setHasStarted,
    setIsInteractive,
    setTasks,
    setCurrentTaskIndex: setCurrentTaskIndexEnhanced,
    setIsProcessing,
    setResults: setResultsEnhanced,
    setShowResults,
    setCurrentQuestion,
    setQuestionAnswer,
    setUserAnswers: setUserAnswersEnhanced,
    setPlanningContext,
    setError,
    setCanRetry,
    setUploadedImages: setUploadedImagesEnhanced,
    setIsProcessingImages,
    setDesignStyleGuideState,
    setIsSettingsOpen,
    setUserApiKey,
    setPreferredModel,
    setIsAutonomousMode,
    setActiveTab,
    setChatMessages,
    setChatInput,
    setSidebarWidth,
    setIsResizing,
    setSelectedPlanningSection,

    // Enhanced actions
    addChatMessage,
    removeUploadedImage,
    clearAllImages,
    resetPlanningState,
  }
}
