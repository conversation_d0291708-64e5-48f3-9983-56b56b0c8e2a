import type { PlanningTask } from "@/types/planning"

/**
 * Base planning tasks that are always included
 */
export const getBasePlanningTasks = (): PlanningTask[] => [
  { id: "analyze", title: "Analyze project requirements", completed: false },
  { id: "clarify", title: "Gather additional details", completed: false },
  { id: "summary", title: "Generate project summary", completed: false },
  { id: "techstack", title: "Select technology stack", completed: false },
  { id: "prd", title: "Create requirements document", completed: false },
]

/**
 * Optional tasks based on project type
 */
export const getOptionalTasks = (projectType: string): PlanningTask[] => {
  const tasks: PlanningTask[] = []

  // Add context profile for AI agents
  if (isAIAgentProject(projectType)) {
    tasks.push({ id: "context-profile", title: "Generate AI agent context profile", completed: false })
  }

  // Add database design for apps that need databases
  if (needsDatabaseStep(projectType)) {
    tasks.push({ id: "database", title: "Design database schema", completed: false })
  }

  return tasks
}

/**
 * Common tasks that are usually included
 */
export const getCommonTasks = (): PlanningTask[] => [
  { id: "wireframes", title: "Design UI wireframes", completed: false },
  { id: "design", title: "Create design guidelines", completed: false },
  { id: "filesystem", title: "Plan file structure", completed: false },
  { id: "workflow", title: "Define workflow logic", completed: false },
  { id: "tasks", title: "Break down implementation tasks", completed: false },
  { id: "scaffold", title: "Generate project scaffold", completed: false },
]

/**
 * Check if project is AI agent related
 */
export const isAIAgentProject = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("agent") ||
         lowerType.includes("bot") ||
         lowerType.includes("ai") ||
         lowerType === "ai agent"
}

/**
 * Check if project needs database design step
 */
export const needsDatabaseStep = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("web") ||
         lowerType.includes("app") ||
         lowerType.includes("api") ||
         lowerType.includes("system")
}

/**
 * Generate dynamic task list based on project type
 */
export const generateDynamicTasks = (projectType: string): PlanningTask[] => {
  return [
    ...getBasePlanningTasks(),
    ...getOptionalTasks(projectType),
    ...getCommonTasks()
  ]
}

/**
 * Default model options for the planning agent
 */
export const MODEL_OPTIONS = [
  { value: "anthropic/claude-sonnet-4", label: "Claude Sonnet 4" },
  { value: "anthropic/claude-3.5-sonnet", label: "Claude 3.5 Sonnet" },
  { value: "openai/gpt-4o", label: "GPT-4o" },
  { value: "openai/gpt-4o-mini", label: "GPT-4o Mini" },
] as const

/**
 * Default sidebar width calculation
 */
export const calculateDefaultSidebarWidth = (): number => {
  if (typeof window !== "undefined") {
    return Math.floor((window.innerWidth - 48) / 3)
  }
  return 500 // Default for SSR
}

/**
 * Sidebar resize constraints
 */
export const SIDEBAR_CONSTRAINTS = {
  MIN_WIDTH: 300,
  MAX_WIDTH_RATIO: 0.6,
} as const

/**
 * Section explanations for the planning interface
 */
export const SECTION_EXPLANATIONS: Record<string, string> = {
  'analyze': 'This section breaks down your project idea into key components, identifying the main features, complexity, and technical requirements.',
  'clarify': 'Questions and answers that help refine the project scope and clarify any ambiguous requirements.',
  'summary': 'A concise overview of what your project will accomplish and its main objectives.',
  'techstack': 'The recommended technologies, frameworks, and tools that will be used to build your project.',
  'prd': 'A detailed Product Requirements Document outlining features, user stories, and technical specifications.',
  'database': 'Database schema design including tables, relationships, and data models for your application.',
  'wireframes': 'Visual mockups and user interface designs showing how your application will look and function.',
  'design': 'Design system guidelines including colors, typography, spacing, and component specifications.',
  'filesystem': 'Recommended file and folder structure for organizing your project code and assets.',
  'workflow': 'Business logic and process flows that define how your application will operate.',
  'tasks': 'Detailed breakdown of implementation tasks with priorities and estimated effort.',
  'scaffold': 'Generated project structure with initial code files and configuration.',
  'context-profile': 'AI agent context profile defining capabilities, knowledge domains, and interaction patterns.',
}

/**
 * Animation variants for framer-motion
 */
export const ANIMATION_VARIANTS = {
  fadeIn: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3 }
  },
  slideIn: {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 },
    transition: { duration: 0.2 }
  }
} as const
