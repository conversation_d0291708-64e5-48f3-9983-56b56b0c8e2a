import type { 
  ApiResponse, 
  PlanningStepRequest, 
  AutonomousAnswerRequest,
  ProjectContext 
} from "@/types/planning"

/**
 * Planning API service for handling all planning-related API calls
 */
export class PlanningApiService {
  
  /**
   * Initialize planning process
   */
  static async initializePlanning(context: {
    prompt: string
    isInteractive: boolean
    answers: Record<string, string>
    designStyleGuide?: any
    hasImages: boolean
    userPreferences?: {
      model: string
      apiKey?: string
      autonomousMode: boolean
    }
  }): Promise<ApiResponse> {
    try {
      const response = await fetch("/api/planning", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(context),
      })

      const responseText = await response.text()
      console.log("Initial API response:", responseText)

      let result
      try {
        result = JSON.parse(responseText)
      } catch (parseError) {
        console.error("Failed to parse initial response:", parseError)
        throw new Error("Invalid JSON response from planning API")
      }

      if (!response.ok) {
        console.error("API returned error:", result)
        throw new Error(`API Error: ${response.status} - ${result.error || "Unknown error"}`)
      }

      return {
        success: true,
        data: result,
        needsInput: result.needsInput,
        question: result.question
      }
    } catch (error) {
      console.error("Planning initialization error:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      }
    }
  }

  /**
   * Process a planning step
   */
  static async processStep(request: PlanningStepRequest): Promise<ApiResponse> {
    try {
      const response = await fetch("/api/planning/step", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error("Step API error:", errorText)
        throw new Error(`API Error: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      console.log(`Step ${request.step} completed:`, result)

      return {
        success: true,
        data: result,
        needsInput: result.needsInput,
        question: result.question
      }
    } catch (error) {
      console.error(`Error processing step ${request.step}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      }
    }
  }

  /**
   * Get autonomous answer for a question
   */
  static async getAutonomousAnswer(request: AutonomousAnswerRequest): Promise<ApiResponse> {
    try {
      const response = await fetch("/api/planning/autonomous-answer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error("Autonomous answer API error:", errorText)
        throw new Error(`API Error: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      console.log("Autonomous answer received:", result)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error("Error getting autonomous answer:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      }
    }
  }

  /**
   * Process images with design agent
   */
  static async processImages(images: File[]): Promise<ApiResponse> {
    try {
      const formData = new FormData()
      images.forEach((image, index) => {
        formData.append('images', image)
      })

      const response = await fetch("/api/design-agent", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.warn("Design agent failed:", errorText)
        return {
          success: false,
          error: `Design agent failed: ${errorText}`
        }
      }

      const result = await response.json()
      console.log("Design agent generated style guide")

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error("Design agent error:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Design agent error"
      }
    }
  }

  /**
   * Retry a failed operation
   */
  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error("Unknown error")
        console.warn(`Attempt ${attempt} failed:`, lastError.message)
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt))
        }
      }
    }

    throw lastError!
  }

  /**
   * Check API health
   */
  static async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch("/api/health", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })
      return response.ok
    } catch (error) {
      console.error("Health check failed:", error)
      return false
    }
  }
}
