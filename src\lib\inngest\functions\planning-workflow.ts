/**
 * Planning Workflow Functions
 * Simplified version for the refactored planning agent
 */

// Mock planning workflow functions for build compatibility
export const planningWorkflow = {
  id: 'planning-workflow',
  name: 'Planning Workflow',
  
  trigger: {
    event: 'planning/start'
  },
  
  handler: async (event: any) => {
    console.log('Mock planning workflow triggered:', event)
    return { success: true, message: 'Planning workflow completed' }
  }
}

export const stepProcessor = {
  id: 'step-processor',
  name: 'Step Processor',
  
  trigger: {
    event: 'planning/step'
  },
  
  handler: async (event: any) => {
    console.log('Mock step processor triggered:', event)
    return { success: true, message: 'Step processed' }
  }
}

export const functions = [planningWorkflow, stepProcessor]
