/**
 * Planning Graph for managing step dependencies
 * Simplified version for the refactored planning agent
 */

interface PlanningStep {
  id: string
  title: string
  dependencies: string[]
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: any
}

class PlanningGraph {
  private steps = new Map<string, PlanningStep>()
  private executionOrder: string[] = []

  addStep(step: PlanningStep): void {
    this.steps.set(step.id, step)
    this.updateExecutionOrder()
  }

  getStep(stepId: string): PlanningStep | undefined {
    return this.steps.get(stepId)
  }

  updateStepStatus(stepId: string, status: PlanningStep['status'], result?: any): void {
    const step = this.steps.get(stepId)
    if (step) {
      step.status = status
      if (result) {
        step.result = result
      }
      this.steps.set(stepId, step)
    }
  }

  getNextStep(): string | null {
    for (const stepId of this.executionOrder) {
      const step = this.steps.get(stepId)
      if (step && step.status === 'pending' && this.areDependenciesMet(stepId)) {
        return stepId
      }
    }
    return null
  }

  areDependenciesMet(stepId: string): boolean {
    const step = this.steps.get(stepId)
    if (!step) return false

    return step.dependencies.every(depId => {
      const depStep = this.steps.get(depId)
      return depStep && depStep.status === 'completed'
    })
  }

  getAllSteps(): PlanningStep[] {
    return Array.from(this.steps.values())
  }

  getCompletedSteps(): PlanningStep[] {
    return this.getAllSteps().filter(step => step.status === 'completed')
  }

  getPendingSteps(): PlanningStep[] {
    return this.getAllSteps().filter(step => step.status === 'pending')
  }

  getRunningSteps(): PlanningStep[] {
    return this.getAllSteps().filter(step => step.status === 'running')
  }

  isComplete(): boolean {
    return this.getAllSteps().every(step => step.status === 'completed')
  }

  hasFailed(): boolean {
    return this.getAllSteps().some(step => step.status === 'failed')
  }

  getProgress(): number {
    const total = this.steps.size
    const completed = this.getCompletedSteps().length
    return total > 0 ? (completed / total) * 100 : 0
  }

  private updateExecutionOrder(): void {
    // Simple topological sort for dependency resolution
    const visited = new Set<string>()
    const temp = new Set<string>()
    const order: string[] = []

    const visit = (stepId: string) => {
      if (temp.has(stepId)) {
        throw new Error(`Circular dependency detected involving step: ${stepId}`)
      }
      if (visited.has(stepId)) {
        return
      }

      temp.add(stepId)
      const step = this.steps.get(stepId)
      if (step) {
        step.dependencies.forEach(depId => {
          if (this.steps.has(depId)) {
            visit(depId)
          }
        })
      }
      temp.delete(stepId)
      visited.add(stepId)
      order.push(stepId)
    }

    Array.from(this.steps.keys()).forEach(stepId => {
      if (!visited.has(stepId)) {
        visit(stepId)
      }
    })

    this.executionOrder = order
  }

  reset(): void {
    this.steps.clear()
    this.executionOrder = []
  }

  toJSON(): any {
    return {
      steps: Array.from(this.steps.entries()),
      executionOrder: this.executionOrder,
      progress: this.getProgress(),
      isComplete: this.isComplete(),
      hasFailed: this.hasFailed()
    }
  }
}

// Create default planning graph with standard steps
export function createDefaultPlanningGraph(): PlanningGraph {
  const graph = new PlanningGraph()

  // Add standard planning steps with dependencies
  const steps: PlanningStep[] = [
    { id: 'analyze', title: 'Analyze project requirements', dependencies: [], status: 'pending' },
    { id: 'clarify', title: 'Gather additional details', dependencies: ['analyze'], status: 'pending' },
    { id: 'summary', title: 'Generate project summary', dependencies: ['clarify'], status: 'pending' },
    { id: 'techstack', title: 'Select technology stack', dependencies: ['summary'], status: 'pending' },
    { id: 'prd', title: 'Create requirements document', dependencies: ['techstack'], status: 'pending' },
    { id: 'wireframes', title: 'Design UI wireframes', dependencies: ['prd'], status: 'pending' },
    { id: 'design', title: 'Create design guidelines', dependencies: ['wireframes'], status: 'pending' },
    { id: 'filesystem', title: 'Plan file structure', dependencies: ['design'], status: 'pending' },
    { id: 'workflow', title: 'Define workflow logic', dependencies: ['filesystem'], status: 'pending' },
    { id: 'tasks', title: 'Break down implementation tasks', dependencies: ['workflow'], status: 'pending' },
    { id: 'scaffold', title: 'Generate project scaffold', dependencies: ['tasks'], status: 'pending' }
  ]

  steps.forEach(step => graph.addStep(step))
  return graph
}

export { PlanningGraph }
export type { PlanningStep }
