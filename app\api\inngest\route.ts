/**
 * Inngest API Route
 * Simplified version for build compatibility
 */

import { NextRequest, NextResponse } from "next/server"

// Simplified handlers for build compatibility
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Inngest endpoint - GET",
    status: "ok"
  })
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: "Inngest endpoint - POST",
    status: "ok"
  })
}

export async function PUT(request: NextRequest) {
  return NextResponse.json({
    message: "Inngest endpoint - PUT",
    status: "ok"
  })
}
