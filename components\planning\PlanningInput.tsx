"use client"

import React, { useRef } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Paperclip, X } from "lucide-react"
import Image from "next/image"
import Globe from "@/components/Globe"
import { ANIMATION_VARIANTS } from "@/lib/planning-constants"

interface PlanningInputProps {
  userPrompt: string
  setUserPrompt: (prompt: string) => void
  uploadedImages: File[]
  setUploadedImages: (images: File[]) => void
  onStartPlanning: () => void
  onImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
  onRemoveImage: (index: number) => void
  onClearAllImages: () => void
  isProcessing: boolean
}

export function PlanningInput({
  userPrompt,
  setUserPrompt,
  uploadedImages,
  setUploadedImages,
  onStartPlanning,
  onImageUpload,
  onRemoveImage,
  onClearAllImages,
  isProcessing
}: PlanningInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const triggerImageUpload = () => {
    fileInputRef.current?.click()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (userPrompt.trim()) {
        onStartPlanning()
      }
    }
  }

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center p-4 relative">
      {/* Background Globe */}
      <div className="absolute inset-0 flex items-center justify-center opacity-20">
        <Globe />
      </div>

      {/* Main Content */}
      <motion.div 
        className="relative z-10 w-full max-w-2xl"
        {...ANIMATION_VARIANTS.fadeIn}
      >
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            <span className="text-white">AP3</span>
            <span className="text-[#ff2d55]" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
          </h1>
          <p className="text-gray-400 text-lg md:text-xl">
            AI-powered project planning and development
          </p>
        </div>

        {/* Image Upload Section */}
        {uploadedImages.length > 0 && (
          <motion.div 
            className="mb-6"
            {...ANIMATION_VARIANTS.slideIn}
          >
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-gray-400">
                {uploadedImages.length} image{uploadedImages.length !== 1 ? 's' : ''} uploaded
              </span>
              <Button
                onClick={onClearAllImages}
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white"
              >
                Clear all
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {uploadedImages.map((image, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-800">
                    <Image
                      src={URL.createObjectURL(image)}
                      alt={`Upload ${index + 1}`}
                      width={100}
                      height={100}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <Button
                    onClick={() => onRemoveImage(index)}
                    variant="ghost"
                    size="sm"
                    className="absolute -top-2 -right-2 w-6 h-6 p-0 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Input Section */}
        <div className="bg-white rounded-3xl p-1 shadow-2xl">
          <div className="flex items-end gap-0">
            {/* Attachment Button */}
            <Button
              onClick={triggerImageUpload}
              variant="ghost"
              className="flex-shrink-0 h-12 w-12 text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors duration-200"
              style={{
                borderTopLeftRadius: '1.5rem',
                borderBottomLeftRadius: '1.5rem',
                borderTopRightRadius: '0',
                borderBottomRightRadius: '0'
              }}
            >
              <Paperclip className="w-5 h-5" />
            </Button>

            {/* Text Input */}
            <div className="flex-1 relative flex items-center">
              <textarea
                value={userPrompt}
                onChange={(e) => {
                  setUserPrompt(e.target.value)
                  // Auto-resize logic
                  const textarea = e.target as HTMLTextAreaElement
                  textarea.style.height = 'auto'
                  const newHeight = Math.min(Math.max(textarea.scrollHeight, 48), 240) // 48px min, 240px max (10 lines)
                  textarea.style.height = `${newHeight}px`
                }}
                onKeyDown={handleKeyDown}
                placeholder="What would you like to build?"
                className="w-full h-12 max-h-[240px] py-3 px-4 text-base text-black placeholder-gray-500 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 leading-6 scrollbar-hide"
                style={{
                  height: '48px'
                }}
                autoFocus
                disabled={isProcessing}
              />
            </div>

            {/* Submit Button */}
            <Button
              onClick={onStartPlanning}
              disabled={!userPrompt.trim() || isProcessing}
              className="flex-shrink-0 h-12 w-12 text-white bg-[#ff2d55] border-0 hover:bg-[#e6254d] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                borderTopRightRadius: '1.5rem',
                borderBottomRightRadius: '1.5rem',
                borderTopLeftRadius: '0',
                borderBottomLeftRadius: '0'
              }}
            >
              <span className="sr-only">Start</span>
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                <path d="M4 3v14l12-7L4 3z" fill="white"/>
              </svg>
            </Button>
          </div>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={onImageUpload}
            className="hidden"
          />
        </div>

        {/* Helper Text */}
        <p className="text-center text-gray-500 text-sm mt-4">
          Describe your project idea and optionally upload design mockups or references
        </p>
      </motion.div>
    </div>
  )
}
