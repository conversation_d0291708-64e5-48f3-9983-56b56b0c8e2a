import { NextRequest, NextResponse } from "next/server"
import { aiService } from "@/lib/ai-service"

export async function POST(request: NextRequest) {
  try {
    const { prompt, analysis, question, context } = await request.json()

    if (!prompt || !question) {
      return NextResponse.json({ error: "Prompt and question are required" }, { status: 400 })
    }

    console.log("🤖 Autonomous question answering for:", question.question)

    // Use the AI service to answer the question autonomously
    const autonomousAnswer = await aiService.answerQuestionsAutonomously(
      prompt,
      analysis,
      [question]
    )

    console.log("✅ Autonomous answer generated successfully")

    // Extract the answer for the specific question
    const answer = autonomousAnswer.answers?.[question.id]
    if (!answer) {
      throw new Error("No autonomous answer generated for question")
    }

    return NextResponse.json({
      success: true,
      answer: answer,
      reasoning: autonomousAnswer.reasoning,
      confidence: autonomousAnswer.confidence,
      assumptions: autonomousAnswer.assumptions || [],
      alternatives: [],
      recommendations: [],
      riskAssessment: []
    })

  } catch (error) {
    console.error("❌ Autonomous answer generation failed:", error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Failed to generate autonomous answer",
      // Provide a fallback answer
      answer: "Standard implementation following industry best practices",
      reasoning: "Fallback to standard approach due to processing error",
      confidence: "medium",
      assumptions: ["Using industry standard practices"],
      alternatives: ["Custom implementation based on specific requirements"]
    }, { status: 500 })
  }
}
