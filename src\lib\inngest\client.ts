/**
 * Inngest Client Configuration
 * Simplified version for the refactored planning agent
 */

// Mock Inngest client for build compatibility
export const inngest = {
  send: async (event: any) => {
    console.log('Mock Inngest event:', event)
    return { id: 'mock-event-id' }
  }
}

export const sendPlanningEvent = async (eventName: string, data: any) => {
  console.log(`Mock planning event: ${eventName}`, data)
  return { success: true, id: 'mock-event-id' }
}
