/**
 * Progress Tracker for planning operations
 * Simplified version for the refactored planning agent
 */

interface SessionData {
  sessionId: string
  startTime: Date
  prompt: string
  status: 'active' | 'completed' | 'failed'
}

interface StepData {
  sessionId: string
  step: string
  startTime: Date
  endTime?: Date
  status: 'running' | 'completed' | 'failed'
  result?: any
}

class ProgressTracker {
  private sessions = new Map<string, SessionData>()
  private steps = new Map<string, StepData[]>()

  trackSessionStart(sessionId: string, prompt: string): void {
    console.log(`📊 Starting session tracking: ${sessionId}`)
    
    this.sessions.set(sessionId, {
      sessionId,
      startTime: new Date(),
      prompt,
      status: 'active'
    })
    
    this.steps.set(sessionId, [])
  }

  trackStepStart(sessionId: string, step: string): void {
    console.log(`📊 Starting step tracking: ${sessionId} - ${step}`)
    
    const sessionSteps = this.steps.get(sessionId) || []
    
    sessionSteps.push({
      sessionId,
      step,
      startTime: new Date(),
      status: 'running'
    })
    
    this.steps.set(sessionId, sessionSteps)
  }

  trackStepComplete(sessionId: string, step: string, result?: any): void {
    console.log(`📊 Completing step tracking: ${sessionId} - ${step}`)
    
    const sessionSteps = this.steps.get(sessionId) || []
    const stepIndex = sessionSteps.findIndex(s => s.step === step && s.status === 'running')
    
    if (stepIndex >= 0) {
      sessionSteps[stepIndex] = {
        ...sessionSteps[stepIndex],
        endTime: new Date(),
        status: 'completed',
        result
      }
      
      this.steps.set(sessionId, sessionSteps)
    }
  }

  trackStepFailed(sessionId: string, step: string, error: string): void {
    console.log(`📊 Step failed: ${sessionId} - ${step} - ${error}`)
    
    const sessionSteps = this.steps.get(sessionId) || []
    const stepIndex = sessionSteps.findIndex(s => s.step === step && s.status === 'running')
    
    if (stepIndex >= 0) {
      sessionSteps[stepIndex] = {
        ...sessionSteps[stepIndex],
        endTime: new Date(),
        status: 'failed',
        result: { error }
      }
      
      this.steps.set(sessionId, sessionSteps)
    }
  }

  trackSessionComplete(sessionId: string): void {
    console.log(`📊 Completing session tracking: ${sessionId}`)
    
    const session = this.sessions.get(sessionId)
    if (session) {
      session.status = 'completed'
      this.sessions.set(sessionId, session)
    }
  }

  trackSessionFailed(sessionId: string, error: string): void {
    console.log(`📊 Session failed: ${sessionId} - ${error}`)
    
    const session = this.sessions.get(sessionId)
    if (session) {
      session.status = 'failed'
      this.sessions.set(sessionId, session)
    }
  }

  getSessionProgress(sessionId: string): any {
    const session = this.sessions.get(sessionId)
    const steps = this.steps.get(sessionId) || []
    
    if (!session) {
      return null
    }
    
    const completedSteps = steps.filter(s => s.status === 'completed').length
    const totalSteps = steps.length
    const currentStep = steps.find(s => s.status === 'running')
    
    return {
      sessionId,
      status: session.status,
      progress: totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0,
      currentStep: currentStep?.step || null,
      completedSteps,
      totalSteps,
      startTime: session.startTime,
      steps: steps.map(step => ({
        step: step.step,
        status: step.status,
        startTime: step.startTime,
        endTime: step.endTime,
        duration: step.endTime ? step.endTime.getTime() - step.startTime.getTime() : null
      }))
    }
  }

  getAllSessions(): SessionData[] {
    return Array.from(this.sessions.values())
  }

  clearSession(sessionId: string): void {
    this.sessions.delete(sessionId)
    this.steps.delete(sessionId)
  }

  clearAllSessions(): void {
    this.sessions.clear()
    this.steps.clear()
  }
}

// Create singleton instance
const progressTracker = new ProgressTracker()

// Export functions for backward compatibility
export const trackSessionStart = (sessionId: string, prompt: string) => 
  progressTracker.trackSessionStart(sessionId, prompt)

export const trackStepStart = (sessionId: string, step: string) => 
  progressTracker.trackStepStart(sessionId, step)

export const trackStepComplete = (sessionId: string, step: string, result?: any) => 
  progressTracker.trackStepComplete(sessionId, step, result)

export const trackStepFailed = (sessionId: string, step: string, error: string) => 
  progressTracker.trackStepFailed(sessionId, step, error)

export const trackSessionComplete = (sessionId: string) => 
  progressTracker.trackSessionComplete(sessionId)

export const trackSessionFailed = (sessionId: string, error: string) => 
  progressTracker.trackSessionFailed(sessionId, error)

export const getSessionProgress = (sessionId: string) => 
  progressTracker.getSessionProgress(sessionId)

export { progressTracker }
