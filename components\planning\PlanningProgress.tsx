"use client"

import React, { useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, AlertCircle, MessageCircle } from "lucide-react"
import type { PlanningTask, Question } from "@/types/planning"
import { ANIMATION_VARIANTS } from "@/lib/planning-constants"

interface PlanningProgressProps {
  tasks: PlanningTask[]
  currentTaskIndex: number
  isProcessing: boolean
  error: string | null
  canRetry: boolean
  currentQuestion: Question | null
  questionAnswer: string
  setQuestionAnswer: (answer: string) => void
  onRetry: () => void
  onQuestionSubmit: () => void
  onQuestionKeyPress: (e: React.KeyboardEvent) => void
}

export function PlanningProgress({
  tasks,
  currentTaskIndex,
  isProcessing,
  error,
  canRetry,
  currentQuestion,
  questionAnswer,
  setQuestionAnswer,
  onRetry,
  onQuestionSubmit,
  onQuestionKeyPress
}: PlanningProgressProps) {
  const taskListRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to current task
  useEffect(() => {
    if (taskListRef.current && currentTaskIndex >= 0) {
      const taskElements = taskListRef.current.querySelectorAll('[data-task-index]')
      const currentTaskElement = taskElements[currentTaskIndex] as HTMLElement

      if (currentTaskElement) {
        const container = taskListRef.current
        const containerHeight = container.clientHeight
        const taskTop = currentTaskElement.offsetTop
        const taskHeight = currentTaskElement.offsetHeight

        // Calculate scroll position to center the current task
        const scrollTop = taskTop - (containerHeight / 2) + (taskHeight / 2)

        container.scrollTo({
          top: Math.max(0, scrollTop),
          behavior: 'smooth'
        })
      }
    }
  }, [currentTaskIndex, tasks.length])

  return (
    <div className="flex flex-col max-h-80">
      {/* Header */}
      <div className="p-3 border-b border-gray-800">
        <h3 className="text-sm font-medium text-white">Progress</h3>
        <p className="text-xs text-gray-400">
          {currentTaskIndex >= 0 ? `Step ${currentTaskIndex + 1} of ${tasks.length}` : 'Ready to start'}
        </p>
      </div>

      {/* Task List */}
      <div
        ref={taskListRef}
        className="flex-1 overflow-y-auto p-3 space-y-2 max-h-48"
      >
        {tasks.map((task, index) => {
          const isActive = index === currentTaskIndex
          const isCompleted = task.completed || index < currentTaskIndex
          const isPending = index > currentTaskIndex

          return (
            <motion.div
              key={task.id}
              data-task-index={index}
              className={`
                p-2 rounded border transition-all duration-300
                ${isActive
                  ? 'bg-blue-500/10 border-blue-500/30'
                  : isCompleted
                    ? 'bg-green-500/10 border-green-500/30'
                    : 'bg-gray-800/50 border-gray-700'
                }
              `}
              {...ANIMATION_VARIANTS.slideIn}
              transition={{ delay: index * 0.1 }}
            >
              <div className="flex items-center gap-2">
                {/* Status Icon */}
                <div className={`
                  w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold
                  ${isActive
                    ? 'bg-blue-500 text-white'
                    : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-600 text-gray-300'
                  }
                `}>
                  {isActive && isProcessing ? (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  ) : isCompleted ? (
                    '✓'
                  ) : (
                    index + 1
                  )}
                </div>

                {/* Task Title */}
                <div className="flex-1 min-w-0">
                  <h4 className={`
                    text-sm font-medium truncate
                    ${isActive
                      ? 'text-blue-400'
                      : isCompleted
                        ? 'text-green-400'
                        : 'text-gray-300'
                    }
                  `}>
                    {task.title}
                  </h4>
                  {isActive && isProcessing && (
                    <p className="text-xs text-gray-400">
                      Processing...
                    </p>
                  )}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Question Dialog */}
      <AnimatePresence>
        {currentQuestion && (
          <motion.div
            className="p-3 border-t border-gray-800 bg-gray-900/50"
            {...ANIMATION_VARIANTS.fadeIn}
          >
            <div className="flex items-start gap-3 mb-3">
              <MessageCircle className="w-5 h-5 text-blue-400 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-white mb-2">
                  Question Required
                </h4>
                <p className="text-sm text-gray-300 mb-3">
                  {currentQuestion.question}
                </p>
                
                {currentQuestion.type === 'select' && currentQuestion.options ? (
                  <select
                    value={questionAnswer}
                    onChange={(e) => setQuestionAnswer(e.target.value)}
                    className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                  >
                    <option value="">Select an option...</option>
                    {currentQuestion.options.map((option, index) => (
                      <option key={index} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                ) : (
                  <textarea
                    value={questionAnswer}
                    onChange={(e) => setQuestionAnswer(e.target.value)}
                    onKeyPress={onQuestionKeyPress}
                    placeholder="Type your answer..."
                    className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white text-sm resize-none"
                    rows={2}
                  />
                )}
                
                <Button
                  onClick={onQuestionSubmit}
                  disabled={!questionAnswer.trim()}
                  className="mt-2 w-full bg-blue-600 hover:bg-blue-700"
                  size="sm"
                >
                  Submit Answer
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            className="p-3 border-t border-red-800 bg-red-900/20"
            {...ANIMATION_VARIANTS.fadeIn}
          >
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-400 mb-1">
                  Error Occurred
                </h4>
                <p className="text-sm text-red-300 mb-3">
                  {error}
                </p>
                {canRetry && (
                  <Button
                    onClick={onRetry}
                    variant="outline"
                    size="sm"
                    className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                  >
                    Retry
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
