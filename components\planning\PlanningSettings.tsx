"use client"

import React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Settings } from "lucide-react"
import { MODEL_OPTIONS } from "@/lib/planning-constants"

interface PlanningSettingsProps {
  isSettingsOpen: boolean
  setIsSettingsOpen: (open: boolean) => void
  userApiKey: string
  setUserApiKey: (key: string) => void
  preferredModel: string
  setPreferredModel: (model: string) => void
  isAutonomousMode: boolean
  setIsAutonomousMode: (autonomous: boolean) => void
  isInteractive: boolean
  setIsInteractive: (interactive: boolean) => void
}

export function PlanningSettings({
  isSettingsOpen,
  setIsSettingsOpen,
  userApiKey,
  setUserApiKey,
  preferredModel,
  setPreferredModel,
  isAutonomousMode,
  setIsAutonomousMode,
  isInteractive,
  setIsInteractive
}: PlanningSettingsProps) {
  return (
    <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-white"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="text-white">Planning Settings</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Model Selection */}
          <div className="space-y-2">
            <Label htmlFor="model-select" className="text-sm font-medium text-gray-300">
              AI Model
            </Label>
            <Select value={preferredModel} onValueChange={setPreferredModel}>
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                {MODEL_OPTIONS.map((option) => (
                  <SelectItem 
                    key={option.value} 
                    value={option.value}
                    className="text-white hover:bg-gray-700"
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-400">
              Choose the AI model for planning and code generation
            </p>
          </div>

          {/* API Key */}
          <div className="space-y-2">
            <Label htmlFor="api-key" className="text-sm font-medium text-gray-300">
              API Key (Optional)
            </Label>
            <Input
              id="api-key"
              type="password"
              value={userApiKey}
              onChange={(e) => setUserApiKey(e.target.value)}
              placeholder="Enter your API key"
              className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
            />
            <p className="text-xs text-gray-400">
              Provide your own API key for higher rate limits
            </p>
          </div>

          {/* Planning Mode */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-300">
                  Interactive Mode
                </Label>
                <p className="text-xs text-gray-400">
                  Ask questions during planning process
                </p>
              </div>
              <Switch
                checked={isInteractive}
                onCheckedChange={setIsInteractive}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-sm font-medium text-gray-300">
                  Autonomous Mode
                </Label>
                <p className="text-xs text-gray-400">
                  AI answers questions automatically
                </p>
              </div>
              <Switch
                checked={isAutonomousMode}
                onCheckedChange={setIsAutonomousMode}
              />
            </div>
          </div>

          {/* Mode Explanation */}
          <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <h4 className="text-sm font-medium text-blue-400 mb-2">
              Planning Modes
            </h4>
            <div className="space-y-2 text-xs text-gray-300">
              <div>
                <strong>Interactive Off + Autonomous Off:</strong> Fast planning without questions
              </div>
              <div>
                <strong>Interactive On + Autonomous Off:</strong> Manual question answering
              </div>
              <div>
                <strong>Interactive On + Autonomous On:</strong> AI answers questions automatically
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4">
            <Button
              onClick={() => setIsSettingsOpen(false)}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
