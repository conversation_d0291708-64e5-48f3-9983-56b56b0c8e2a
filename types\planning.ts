export interface PlanningTask {
  id: string
  title: string
  completed: boolean
}

export interface Question {
  id: string
  question: string
  type?: 'text' | 'select' | 'multiselect'
  options?: string[]
  required?: boolean
}

export interface ProjectContext {
  prompt: string
  isInteractive: boolean
  userAnswers: Record<string, string>
  designStyleGuide?: any
  hasImages: boolean
  userPreferences?: {
    model: string
    apiKey?: string
    autonomousMode: boolean
  }
}

export interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}

export interface PlanningState {
  // Core planning state
  userPrompt: string
  hasStarted: boolean
  isInteractive: boolean
  tasks: PlanningTask[]
  currentTaskIndex: number
  isProcessing: boolean
  results: Record<string, any>
  showResults: boolean

  // Question handling
  currentQuestion: Question | null
  questionAnswer: string
  userAnswers: Record<string, string>
  planningContext: any

  // Error handling
  error: string | null
  canRetry: boolean

  // Image handling
  uploadedImages: File[]
  isProcessingImages: boolean
  designStyleGuideState: string | null

  // Settings
  isSettingsOpen: boolean
  userApiKey: string
  preferredModel: string
  isAutonomousMode: boolean

  // UI state
  activeTab: "preview" | "code" | "planning" | "graph"
  chatMessages: ChatMessage[]
  chatInput: string
  sidebarWidth: number
  isResizing: boolean
  selectedPlanningSection: string | null
}

export interface PlanningActions {
  // Core actions
  setUserPrompt: (prompt: string) => void
  setHasStarted: (started: boolean) => void
  setIsInteractive: (interactive: boolean) => void
  setTasks: (tasks: PlanningTask[]) => void
  setCurrentTaskIndex: (index: number) => void
  setIsProcessing: (processing: boolean) => void
  setResults: (results: Record<string, any>) => void
  setShowResults: (show: boolean) => void

  // Question actions
  setCurrentQuestion: (question: Question | null) => void
  setQuestionAnswer: (answer: string) => void
  setUserAnswers: (answers: Record<string, string>) => void
  setPlanningContext: (context: any) => void

  // Error actions
  setError: (error: string | null) => void
  setCanRetry: (canRetry: boolean) => void

  // Image actions
  setUploadedImages: (images: File[]) => void
  setIsProcessingImages: (processing: boolean) => void
  setDesignStyleGuideState: (guide: string | null) => void

  // Settings actions
  setIsSettingsOpen: (open: boolean) => void
  setUserApiKey: (key: string) => void
  setPreferredModel: (model: string) => void
  setIsAutonomousMode: (autonomous: boolean) => void

  // UI actions
  setActiveTab: (tab: "preview" | "code" | "planning" | "graph") => void
  setChatMessages: (messages: ChatMessage[]) => void
  setChatInput: (input: string) => void
  setSidebarWidth: (width: number) => void
  setIsResizing: (resizing: boolean) => void
  setSelectedPlanningSection: (section: string | null) => void

  // Enhanced actions
  addChatMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void
  removeUploadedImage: (index: number) => void
  clearAllImages: () => void
  resetPlanningState: () => void
}

export interface ApiResponse {
  success: boolean
  data?: any
  error?: string
  needsInput?: boolean
  question?: Question
}

export interface PlanningStepRequest {
  step: string
  context: ProjectContext
  answer?: string
}

export interface AutonomousAnswerRequest {
  prompt: string
  analysis: any
  question: Question
  context?: any
}
