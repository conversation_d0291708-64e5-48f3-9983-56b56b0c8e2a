"use client"

import React from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"
import { ResultsView } from "@/components/results-view"
import { ANIMATION_VARIANTS, SECTION_EXPLANATIONS } from "@/lib/planning-constants"

interface PlanningResultsProps {
  results: Record<string, any>
  showResults: boolean
  setShowResults: (show: boolean) => void
  selectedPlanningSection: string | null
  setSelectedPlanningSection: (section: string | null) => void
  activeTab: "preview" | "code" | "planning" | "graph"
  setActiveTab: (tab: "preview" | "code" | "planning" | "graph") => void
}

export function PlanningResults({
  results,
  showResults,
  setShowResults,
  selectedPlanningSection,
  setSelectedPlanningSection,
  activeTab,
  setActiveTab
}: PlanningResultsProps) {
  const hasResults = Object.keys(results).length > 0

  if (!hasResults) {
    return (
      <div className="h-full flex items-center justify-center text-gray-400">
        <div className="text-center">
          <p className="text-lg mb-2">No results yet</p>
          <p className="text-sm">Start planning to see results here</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-white">Planning Results</h2>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowResults(!showResults)}
              className="text-gray-400 hover:text-white"
            >
              {showResults ? 'Hide' : 'Show'} Details
            </Button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-800">
        <div className="flex">
          {(['preview', 'code', 'planning', 'graph'] as const).map((tab) => (
            <Button
              key={tab}
              variant="ghost"
              onClick={() => setActiveTab(tab)}
              className={`
                px-4 py-2 text-sm font-medium rounded-none border-b-2 transition-colors
                ${activeTab === tab
                  ? 'border-blue-500 text-blue-400 bg-blue-500/10'
                  : 'border-transparent text-gray-400 hover:text-white hover:bg-gray-800'
                }
              `}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'planning' && (
          <motion.div 
            className="h-full overflow-y-auto"
            {...ANIMATION_VARIANTS.fadeIn}
          >
            {/* Section Navigation */}
            <div className="p-4 border-b border-gray-800">
              <div className="flex flex-wrap gap-2">
                {Object.keys(results).map((section) => (
                  <Button
                    key={section}
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedPlanningSection(
                      selectedPlanningSection === section ? null : section
                    )}
                    className={`
                      text-xs
                      ${selectedPlanningSection === section
                        ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                        : 'text-gray-400 hover:text-white hover:bg-gray-800'
                      }
                    `}
                  >
                    {section}
                  </Button>
                ))}
              </div>
            </div>

            {/* Section Content */}
            <div className="p-4">
              {selectedPlanningSection ? (
                <motion.div {...ANIMATION_VARIANTS.slideIn}>
                  {/* Section Explanation */}
                  {SECTION_EXPLANATIONS[selectedPlanningSection] && (
                    <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-4">
                      <div className="text-blue-400 text-sm">
                        {SECTION_EXPLANATIONS[selectedPlanningSection]}
                      </div>
                    </div>
                  )}

                  {/* Section Data */}
                  <div className="bg-gray-800 rounded-lg p-4">
                    <h3 className="text-white font-medium mb-3 capitalize">
                      {selectedPlanningSection.replace(/([A-Z])/g, " $1").trim()}
                    </h3>
                    <div className="text-gray-300 text-sm">
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(results[selectedPlanningSection], null, 2)}
                      </pre>
                    </div>
                  </div>
                </motion.div>
              ) : (
                <div className="text-center text-gray-400 py-8">
                  <p>Select a section above to view details</p>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {activeTab === 'preview' && (
          <motion.div 
            className="h-full p-4"
            {...ANIMATION_VARIANTS.fadeIn}
          >
            <div className="bg-gray-800 rounded-lg p-6 h-full flex items-center justify-center">
              <div className="text-center text-gray-400">
                <p className="text-lg mb-2">Preview Coming Soon</p>
                <p className="text-sm">Live preview of your project will appear here</p>
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'code' && (
          <motion.div
            className="h-full"
            {...ANIMATION_VARIANTS.fadeIn}
          >
            <ResultsView
              results={results}
              userPrompt=""
              onBack={() => {}}
            />
          </motion.div>
        )}

        {activeTab === 'graph' && (
          <motion.div 
            className="h-full p-4"
            {...ANIMATION_VARIANTS.fadeIn}
          >
            <div className="bg-gray-800 rounded-lg p-6 h-full flex items-center justify-center">
              <div className="text-center text-gray-400">
                <p className="text-lg mb-2">Dependency Graph</p>
                <p className="text-sm">Visual representation of project dependencies</p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
