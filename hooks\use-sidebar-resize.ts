import { useEffect, useCallback } from "react"
import { calculateDefaultSidebarWidth, SIDEBAR_CONSTRAINTS } from "@/lib/planning-constants"

interface UseSidebarResizeProps {
  sidebarWidth: number
  setSidebarWidth: (width: number) => void
  isResizing: boolean
  setIsResizing: (resizing: boolean) => void
}

/**
 * Custom hook for handling sidebar resize functionality
 */
export function useSidebarResize({
  sidebarWidth,
  setSidebarWidth,
  isResizing,
  setIsResizing
}: UseSidebarResizeProps) {
  
  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = calculateDefaultSidebarWidth()
      setSidebarWidth(calculatedWidth)
    }
  }, [setSidebarWidth])

  // Handle mouse down event to start resizing
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [setIsResizing])

  // Handle resize logic
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return

      const newWidth = e.clientX - 16 // Account for padding
      const minWidth = SIDEBAR_CONSTRAINTS.MIN_WIDTH
      const maxWidth = window.innerWidth * SIDEBAR_CONSTRAINTS.MAX_WIDTH_RATIO

      if (newWidth >= minWidth && newWidth <= maxWidth) {
        setSidebarWidth(newWidth)
      }
    }

    const handleMouseUp = () => {
      setIsResizing(false)
    }

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isResizing, setSidebarWidth, setIsResizing])

  return {
    handleMouseDown
  }
}
