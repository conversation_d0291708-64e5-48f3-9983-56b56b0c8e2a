"use client"

import React, { useEffect, useRef, use<PERSON><PERSON>back } from "react"
import { motion } from "framer-motion"
import type { PlanningTask, Question } from "@/types/planning"
import { usePlanningState } from "@/hooks/use-planning-state"
import { useSidebarResize } from "@/hooks/use-sidebar-resize"
import { PlanningApiService } from "@/services/planning-api"
import { generateDynamicTasks } from "@/lib/planning-constants"
import { PlanningSettings } from "@/components/planning/PlanningSettings"
import { ResultsView } from "@/components/results-view"
import Globe from "@/components/Globe"
import Image from "next/image"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectVal<PERSON> } from "@/components/ui/select"
import { Setting<PERSON>, Loader2, <PERSON>, <PERSON>c<PERSON> } from "lucide-react"

export function PlanningAgent() {
  // Use custom hooks for state management
  const state = usePlanningState()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const taskListRef = useRef<HTMLDivElement>(null)
  const processingRef = useRef<boolean>(false)
  
  // Sidebar resize functionality
  const { handleMouseDown } = useSidebarResize({
    sidebarWidth: state.sidebarWidth,
    setSidebarWidth: state.setSidebarWidth,
    isResizing: state.isResizing,
    setIsResizing: state.setIsResizing
  })

  // Core business logic functions
  const processNextTask = useCallback(async (context: any) => {
    console.log(`processNextTask called with currentTaskIndex: ${state.currentTaskIndex}`)

    // Prevent multiple simultaneous processing
    if (processingRef.current) {
      console.log("Already processing a task, skipping")
      return
    }

    if (state.currentTaskIndex < 0 || state.currentTaskIndex >= state.tasks.length) {
      console.log("Invalid task index, finishing processing")
      state.setIsProcessing(false)
      return
    }

    const currentTask = state.tasks[state.currentTaskIndex]
    if (!currentTask) {
      console.log("No current task found, finishing processing")
      state.setIsProcessing(false)
      return
    }

    // Prevent processing the same task multiple times
    if (currentTask.completed) {
      console.log(`Task ${currentTask.id} already completed, skipping`)
      return
    }

    // Set processing lock
    processingRef.current = true
    console.log(`Processing task: ${currentTask.id}`)

    // Get design style guide from state or context
    const designStyleGuide = state.designStyleGuideState || context.designStyleGuide

    try {
      const response = await PlanningApiService.processStep({
        step: currentTask.id,
        context: {
          ...context,
          prompt: state.userPrompt,
          isInteractive: state.isInteractive,
          userAnswers: context.userAnswers || state.userAnswers, // Use context userAnswers if available
          designStyleGuide: designStyleGuide,
          hasImages: state.uploadedImages.length > 0,
          results: state.results, // Include all accumulated results from previous steps
          userPreferences: {
            model: state.preferredModel,
            apiKey: state.userApiKey || undefined,
            autonomousMode: state.isAutonomousMode
          }
        },
        answer: state.questionAnswer,
      })

      if (!response.success) {
        throw new Error(response.error || "Unknown error occurred")
      }

      const result = response.data

      // Update results
      const newResults = { ...state.results, [currentTask.id]: result }
      state.setResults(newResults)

      // Check if we need user input
      if (result.needsInput && result.question) {
        if (state.isInteractive && !state.isAutonomousMode) {
          // Interactive mode - show question to user
          console.log("Need user input:", result.question.question)
          state.setCurrentQuestion(result.question)
          state.setPlanningContext({ ...context, ...result })
          return
        } else if (state.isAutonomousMode) {
          // Autonomous mode - answer questions automatically
          console.log("Autonomous mode: answering question automatically:", result.question.question)
          try {
            const autonomousResponse = await PlanningApiService.getAutonomousAnswer({
              prompt: state.userPrompt,
              analysis: state.results.analyze || {},
              question: result.question,
              context: { ...context, ...result, results: state.results }
            })

            if (autonomousResponse.success) {
              const answer = autonomousResponse.data.answer
              console.log("Autonomous answer:", answer)
              console.log("Question ID:", result.question.id)
              console.log("Current user answers before update:", state.userAnswers)
              console.log("State object keys:", Object.keys(state))

              // Create updated answers by merging with CONTEXT userAnswers (not state)
              const currentAnswers = context.userAnswers || state.userAnswers || {}
              const newAnswers = { ...currentAnswers, [result.question.id]: answer }
              console.log("Context user answers before update:", currentAnswers)
              console.log("New user answers after update:", newAnswers)

              // Update state for UI display
              state.setUserAnswers(newAnswers)
              state.setQuestionAnswer(answer)
              console.log("State updated for UI, continuing with context answers:", newAnswers)

              // Release processing lock and continue processing with the updated context
              setTimeout(() => {
                processingRef.current = false
                processNextTask({
                  ...context,
                  ...result,
                  userAnswers: newAnswers,  // Use accumulated answers from context
                  results: state.results    // Include all accumulated results
                })
              }, 500)
              return
            } else {
              console.warn("Failed to get autonomous answer, continuing without answer")
            }
          } catch (autonomousError) {
            console.error("Autonomous answer error:", autonomousError)
            // Continue without answer
          }
        }
      }

      // Update planning context while preserving design style guide
      const preservedDesignStyleGuide = designStyleGuide || context.designStyleGuide || state.planningContext?.designStyleGuide || state.designStyleGuideState
      state.setPlanningContext({
        ...context,
        ...result,
        designStyleGuide: preservedDesignStyleGuide
      })

      // Mark current task as completed
      const updatedTasks = state.tasks.map((task, index) =>
        index === state.currentTaskIndex ? { ...task, completed: true } : task
      )
      state.setTasks(updatedTasks)

      // Move to next task
      if (state.currentTaskIndex < state.tasks.length - 1) {
        const nextIndex = state.currentTaskIndex + 1
        console.log(`Moving to next task (${nextIndex})`)
        setTimeout(() => {
          processingRef.current = false // Release lock before moving to next task
          state.setCurrentTaskIndex(nextIndex)
        }, 1000)
      } else {
        console.log("All tasks completed!")
        setTimeout(() => {
          processingRef.current = false // Release lock
          state.setIsProcessing(false)
          // Add completion message to chat
          state.addChatMessage({
            type: 'ai',
            content: "🎉 Planning complete! Your crypto assistant agent development plan is ready. Check the Planning tab for detailed results."
          })
        }, 1000)
      }

    } catch (error) {
      console.error(`Error processing task ${currentTask.id}:`, error)
      processingRef.current = false // Release lock on error
      state.setError(error instanceof Error ? error.message : "Unknown error occurred")
      state.setCanRetry(true)
      state.setIsProcessing(false)
    }
  }, [state])

  const handleStartPlanning = useCallback(async () => {
    if (!state.userPrompt.trim()) return

    console.log("Starting planning process...")
    state.setHasStarted(true)
    state.setIsProcessing(true)
    state.setCurrentTaskIndex(0)
    state.setError(null)

    // Add user message to chat
    state.addChatMessage({
      type: 'user',
      content: state.userPrompt,
    })

    // Add AI response
    setTimeout(() => {
      state.addChatMessage({
        type: 'ai',
        content: "I'll help you build that! Let me start by analyzing your requirements and creating a development plan.",
      })
    }, 500)

    try {
      let designStyleGuide = null

      // Process images if uploaded
      if (state.uploadedImages.length > 0) {
        state.setIsProcessingImages(true)

        try {
          const designResponse = await PlanningApiService.processImages(state.uploadedImages)

          if (designResponse.success) {
            designStyleGuide = designResponse.data.styleGuide
            state.setDesignStyleGuideState(designStyleGuide)
            console.log("Design agent generated style guide - will be added when design step runs")
          } else {
            console.warn("Design agent failed:", designResponse.error)
          }
        } catch (designError) {
          console.error("Design agent error:", designError)
        } finally {
          state.setIsProcessingImages(false)
        }
      }

      // Initialize planning
      const response = await PlanningApiService.initializePlanning({
        prompt: state.userPrompt,
        isInteractive: state.isInteractive,
        answers: state.userAnswers,
        designStyleGuide,
        hasImages: state.uploadedImages.length > 0,
        userPreferences: {
          model: state.preferredModel,
          apiKey: state.userApiKey || undefined,
          autonomousMode: state.isAutonomousMode
        }
      })

      if (!response.success) {
        throw new Error(response.error || "Unknown error occurred")
      }

      console.log("Using AI-powered planning mode")
      console.log("Setting planning context with design style guide:", !!designStyleGuide)
      state.setPlanningContext({
        ...response.data,
        designStyleGuide: designStyleGuide,
      })

      // Generate dynamic tasks based on project type
      const projectType = response.data?.projectType || ""
      const dynamicTasks = generateDynamicTasks(projectType)
      state.setTasks(dynamicTasks)

      // Start processing first task
      setTimeout(() => processNextTask(response.data), 1000)

    } catch (error) {
      console.error("Planning initialization error:", error)
      state.setError(error instanceof Error ? error.message : "Failed to start planning")
      state.setCanRetry(true)
      state.setIsProcessing(false)
    }
  }, [state, processNextTask])

  // Event handlers
  const handleImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const imageFiles = Array.from(files).filter(file => {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
        if (!validTypes.includes(file.type)) {
          console.warn(`Skipping invalid file type: ${file.type}`)
          return false
        }

        const maxSize = 10 * 1024 * 1024 // 10MB
        if (file.size > maxSize) {
          console.warn(`Skipping file too large: ${file.name} (${(file.size / 1024 / 1024).toFixed(1)}MB)`)
          return false
        }

        return true
      })

      if (imageFiles.length > 0) {
        const newImages = [...state.uploadedImages, ...imageFiles]
        state.setUploadedImages(newImages)
      }
    }
    e.target.value = ''
  }, [state])

  const removeImage = useCallback((index: number) => {
    const newImages = state.uploadedImages.filter((_, i) => i !== index)
    state.setUploadedImages(newImages)
  }, [state])

  const triggerImageUpload = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const clearAllImages = useCallback(() => {
    state.setUploadedImages([])
  }, [state])

  // Auto-scroll to current task
  useEffect(() => {
    if (taskListRef.current && state.currentTaskIndex >= 0) {
      const currentTaskElement = taskListRef.current.querySelector(`[data-task-index="${state.currentTaskIndex}"]`)
      if (currentTaskElement) {
        currentTaskElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    }
  }, [state.currentTaskIndex])

  // Auto-process tasks when index changes
  useEffect(() => {
    if (state.currentTaskIndex >= 0 && state.currentTaskIndex < state.tasks.length && state.isProcessing && !state.currentQuestion) {
      const currentTask = state.tasks[state.currentTaskIndex]
      // Only process if the current task is not already completed
      if (currentTask && !currentTask.completed) {
        console.log(`useEffect triggered for task index: ${state.currentTaskIndex}, task: ${currentTask.id}`)
        processNextTask(state.planningContext || {})
      }
    }
  }, [state.currentTaskIndex, state.isProcessing, state.currentQuestion, state.tasks, processNextTask, state.planningContext])

  // Show initial input if not started - Original Landing Page
  if (!state.hasStarted) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4 relative">
        {/* Settings Button - Bottom Left */}
        <Dialog open={state.isSettingsOpen} onOpenChange={state.setIsSettingsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed bottom-6 left-6 h-8 w-8 text-gray-400 hover:text-white transition-colors duration-200 z-50 bg-transparent border-0 p-0"
            >
              <Settings size={20} />
              <span className="sr-only">Settings</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md border-gray-700 text-white" style={{ backgroundColor: '#181818' }}>
            <DialogHeader>
              <DialogTitle className="text-white">Settings</DialogTitle>
            </DialogHeader>
            <div className="space-y-6 py-4">
              {/* API Key Input */}
              <div className="space-y-2">
                <Label htmlFor="api-key" className="text-sm font-medium text-gray-300">
                  OpenRouter API Key
                </Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="sk-or-v1-..."
                  value={state.userApiKey}
                  onChange={(e) => state.setUserApiKey(e.target.value)}
                  className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                />
                <p className="text-xs text-gray-400">
                  Optional: Use your own API key for unlimited usage
                </p>
              </div>

              {/* Model Selection */}
              <div className="space-y-2">
                <Label htmlFor="model" className="text-sm font-medium text-gray-300">
                  Preferred Model
                </Label>
                <Select value={state.preferredModel} onValueChange={state.setPreferredModel}>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="border-gray-600" style={{ backgroundColor: '#000000' }}>
                    <SelectItem value="x-ai/grok-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Grok 4 (X.AI)</SelectItem>
                    <SelectItem value="moonshotai/kimi-k2" className="text-white hover:bg-gray-800 focus:bg-gray-800">Kimi K2 (Moonshot)</SelectItem>
                    <SelectItem value="anthropic/claude-sonnet-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude Sonnet 4 (Default)</SelectItem>
                    <SelectItem value="anthropic/claude-3.7-sonnet:thinking" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet (Thinking)</SelectItem>
                    <SelectItem value="anthropic/claude-3.7-sonnet" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet</SelectItem>
                    <SelectItem value="openai/gpt-4.1" className="text-white hover:bg-gray-800 focus:bg-gray-800">GPT-4.1 (OpenAI)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Mode Toggle */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="autonomous-mode" className="text-sm font-medium text-gray-300">
                    Autonomous Mode
                  </Label>
                  <p className="text-xs text-gray-400">
                    {state.isAutonomousMode ? "AI runs all steps automatically" : "Guided step-by-step planning"}
                  </p>
                </div>
                <Switch
                  id="autonomous-mode"
                  checked={state.isAutonomousMode}
                  onCheckedChange={state.setIsAutonomousMode}
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Powered by AP3X - Bottom Right */}
        <div className="fixed bottom-6 right-6 text-xs text-gray-500 font-medium z-50">
          Powered by{' '}
          <span className="text-white font-semibold">AP3</span>
          <span className="text-[#ff2d55] font-semibold" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
        </div>

        <div className="w-full max-w-2xl text-center space-y-8">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <div className="flex items-center justify-center gap-3 mb-6">
              <Image
                src="/AG3NT.png"
                alt="AG3NT"
                width={102}
                height={34}
                className="object-contain"
                style={{ width: '200px', height: 'auto' }}
              />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Image Upload Preview */}
              {state.uploadedImages.length > 0 && (
                <div className="p-3 rounded-lg" style={{ backgroundColor: '#181818' }}>
                  {state.isProcessingImages && (
                    <div className="flex items-center gap-2 mb-3 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Analyzing images with AI...</span>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2">
                    {state.uploadedImages.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Upload ${index + 1}`}
                          className={`w-16 h-16 object-cover rounded border transition-opacity ${
                            state.isProcessingImages ? 'opacity-50' : 'opacity-100'
                          }`}
                          title={image.name}
                        />
                        {!state.isProcessingImages && (
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X size={12} />
                          </button>
                        )}
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b opacity-0 group-hover:opacity-100 transition-opacity truncate">
                          {image.name}
                        </div>
                      </div>
                    ))}
                  </div>
                  {state.uploadedImages.length > 0 && !state.isProcessingImages && (
                    <div className="mt-2 flex items-center justify-between">
                      <div className="text-xs text-gray-600">
                        {state.uploadedImages.length} image{state.uploadedImages.length > 1 ? 's' : ''} ready for AI analysis
                      </div>
                      <button
                        onClick={clearAllImages}
                        className="text-xs text-red-500 hover:text-red-700 underline"
                      >
                        Clear all
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Input with Paperclip and Send Button */}
              <div className="flex w-full items-end gap-0 rounded-3xl overflow-hidden bg-white shadow-lg border border-gray-100">
                <Button
                  onClick={triggerImageUpload}
                  className="flex-shrink-0 h-12 w-12 text-gray-600 bg-transparent border-0 hover:bg-gray-50 transition-colors duration-200 rounded-l-3xl flex items-center justify-center"
                  title="Upload design reference images for AI analysis"
                  disabled={state.isProcessing || state.isProcessingImages}
                >
                  <Paperclip size={18} />
                  <span className="sr-only">Upload Images</span>
                </Button>

                <div className="flex-1 relative flex items-center">
                  <textarea
                    value={state.userPrompt}
                    onChange={(e) => {
                      state.setUserPrompt(e.target.value)
                      // Auto-resize logic
                      const textarea = e.target as HTMLTextAreaElement
                      textarea.style.height = 'auto'
                      const newHeight = Math.min(Math.max(textarea.scrollHeight, 48), 240) // 48px min, 240px max (10 lines)
                      textarea.style.height = `${newHeight}px`
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        if (state.userPrompt.trim()) {
                          handleStartPlanning()
                        }
                      }
                    }}
                    placeholder="What would you like to build?"
                    className="w-full h-12 max-h-[240px] py-3 px-4 text-base text-black placeholder-gray-500 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 leading-6 scrollbar-hide"
                    style={{
                      height: '48px'
                    }}
                    autoFocus
                  />
                </div>

                <Button
                  onClick={handleStartPlanning}
                  disabled={!state.userPrompt.trim()}
                  className="flex-shrink-0 h-12 w-12 text-white bg-[#ff2d55] border-0 hover:bg-[#e6254d] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    borderTopRightRadius: '1.5rem',
                    borderBottomRightRadius: '1.5rem',
                    borderTopLeftRadius: '0',
                    borderBottomLeftRadius: '0'
                  }}
                >
                  <span className="sr-only">Start</span>
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                    <path d="M4 3v14l12-7L4 3z" fill="white"/>
                  </svg>
                </Button>
              </div>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  // Show results view if we have results and user wants to see them
  if (Object.keys(state.results).length > 0 && state.showResults) {
    return (
      <ResultsView
        results={state.results}
        userPrompt={state.userPrompt}
        onBack={() => {
          state.setShowResults(false)
        }}
      />
    )
  }

  // Main planning interface - original UI structure
  return (
    <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
      {/* Top Navigation Bar */}
      <header className="h-12 md:h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-3 md:px-6">
        <div className="flex items-center gap-3 md:gap-6 min-w-0">
          <div className="flex items-center gap-2 md:gap-3">
            <div className="text-lg md:text-xl font-bold">
              <span className="text-white">AP3</span>
              <span className="text-[#ff2d55]" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
            </div>
          </div>
          <div className="hidden sm:flex items-center gap-1 text-xs text-[#666]">
            <span>Personal</span>
            <span>/</span>
            <span>Project Planning</span>
          </div>
        </div>
        <div className="flex items-center gap-1 md:gap-2">
          <PlanningSettings
            isSettingsOpen={state.isSettingsOpen}
            setIsSettingsOpen={state.setIsSettingsOpen}
            userApiKey={state.userApiKey}
            setUserApiKey={state.setUserApiKey}
            preferredModel={state.preferredModel}
            setPreferredModel={state.setPreferredModel}
            isAutonomousMode={state.isAutonomousMode}
            setIsAutonomousMode={state.setIsAutonomousMode}
            isInteractive={state.isInteractive}
            setIsInteractive={state.setIsInteractive}
          />
          <button className="h-7 md:h-8 px-2 md:px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md">
            <span className="hidden sm:inline">Publish</span>
            <span className="sm:hidden">📤</span>
          </button>
        </div>
      </header>

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden p-4 gap-2">
        {/* Left Sidebar - Chat */}
        <aside
          style={{ width: state.sidebarWidth }}
          className="bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-xl"
        >
          {/* Chat Header */}
          <div className="px-4 md:px-6 py-3 md:py-4 border-b border-[#1a1a1a] flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <h2 className="font-medium text-white text-sm md:text-base">Chat</h2>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <div className="px-4 md:px-6 py-3 md:py-4 space-y-3 md:space-y-4 h-full overflow-y-auto">
              {state.chatMessages.length === 0 ? (
                <div className="text-center text-gray-400 text-sm">
                  Start a conversation to begin planning your project
                </div>
              ) : (
                state.chatMessages.map((message) => (
                  <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[75%] p-3 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-[#ff2d55] text-white'
                        : 'bg-[#1a1a1a] text-gray-200'
                    }`}>
                      <div className="text-sm">{message.content}</div>
                      <div className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                  </div>
                ))
              )}

              {/* Planning Progress Message - Scrolling Card Format */}
              {(state.isProcessing || (!state.isProcessing && state.hasStarted && state.tasks.some(t => t.completed))) && (
                <div className="flex justify-start">
                  <div className="max-w-[75%] p-4 rounded-lg bg-[#1a1a1a] text-gray-200">
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-bold text-white">
                        {state.isProcessing ? (
                          <>
                            <span className="text-white">AG3N</span>
                            <span className="text-[#ff2d55]">T</span>
                            <span className="text-white"> is Planning Your Project</span>
                          </>
                        ) : 'Planning Complete'}
                      </h3>
                    </div>

                    {/* Progressive Task Container - Original behavior */}
                    <div ref={taskListRef} className="h-[180px] overflow-y-auto space-y-2 pr-2">
                      {state.tasks.map((task, originalIndex) => {
                        // Only show tasks that have started or are completed
                        const shouldShow = originalIndex <= state.currentTaskIndex || task.completed
                        if (!shouldShow) return null

                        const isActive = originalIndex === state.currentTaskIndex && state.isProcessing
                        const isCompleted = task.completed

                        return (
                          <div
                            key={task.id}
                            data-task-index={originalIndex}
                            className="flex items-center gap-3 py-2 transition-all duration-300"
                            style={{
                              // Auto-scroll effect: current task gets highlighted
                              transform: isActive ? 'translateX(4px)' : 'translateX(0px)',
                              backgroundColor: isActive ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
                              borderRadius: isActive ? '6px' : '0px',
                              padding: isActive ? '8px' : '8px 0px'
                            }}
                          >
                            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                              {isCompleted ? (
                                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                </div>
                              ) : isActive ? (
                                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
                                  <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
                                </div>
                              ) : (
                                <div className="w-6 h-6 border-2 border-gray-600 rounded-full"></div>
                              )}
                            </div>
                            <span className={`text-sm transition-colors duration-300 ${
                              isCompleted ? 'text-green-400 font-medium' : isActive ? 'text-blue-400 font-medium' : 'text-gray-400'
                            }`}>
                              {task.title}
                            </span>
                            {isActive && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                              </div>
                            )}
                          </div>
                        )
                      }).filter(Boolean)}
                    </div>

                    {!state.isProcessing && (
                      <div className="mt-4 pt-3 border-t border-gray-600 text-center">
                        <button
                          onClick={() => state.setActiveTab("planning")}
                          className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          View detailed plan →
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Chat Input */}
          <div className="shrink-0 p-3 md:p-4 border-t border-[#1a1a1a]">
            <div className="relative bg-[#1a1a1a] border border-[#333] rounded-lg">
              <textarea
                value={state.chatInput}
                onChange={(e) => state.setChatInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    if (state.chatInput.trim()) {
                      state.addChatMessage({
                        type: 'user',
                        content: state.chatInput,
                      })
                      state.setChatInput("")

                      // Add AI response
                      setTimeout(() => {
                        state.addChatMessage({
                          type: 'ai',
                          content: "I'll help you with that! Let me analyze your request and create a plan.",
                        })
                      }, 500)
                    }
                  }
                }}
                placeholder="Ask a follow-up..."
                className="w-full min-h-[48px] max-h-[144px] p-3 pr-24 text-white placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 leading-6 rounded-lg"
              />

              {/* Action Buttons */}
              <div className="absolute bottom-2 md:bottom-3 right-2 md:right-3 flex items-center gap-1 md:gap-2">
                <button className="w-7 h-7 md:w-8 md:h-8 flex items-center justify-center text-gray-400 hover:text-white transition-colors rounded">
                  <svg width="14" height="14" className="md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                </button>
                <button
                  onClick={() => {
                    if (state.chatInput.trim()) {
                      state.addChatMessage({
                        type: 'user',
                        content: state.chatInput,
                      })
                      state.setChatInput("")

                      setTimeout(() => {
                        state.addChatMessage({
                          type: 'ai',
                          content: "I'll help you with that! Let me analyze your request and create a plan.",
                        })
                      }, 500)
                    }
                  }}
                  disabled={!state.chatInput.trim()}
                  className="w-7 h-7 md:w-8 md:h-8 flex items-center justify-center text-gray-400 hover:text-white transition-colors rounded disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg width="14" height="14" className="md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </aside>

        {/* Resize Handle */}
        <div
          className={`w-1 bg-transparent hover:bg-[#333] cursor-col-resize transition-colors ${
            state.isResizing ? 'bg-[#555]' : ''
          }`}
          onMouseDown={handleMouseDown}
        />

        {/* Main Content */}
        <main className="flex-1 bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden shadow-xl">
          {/* Editor Tabs */}
          <div className="h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center px-6">
            <div className="flex items-center gap-1">
              <button
                onClick={() => state.setActiveTab("preview")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  state.activeTab === "preview"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>📋</span>
                Preview
              </button>
              <button
                onClick={() => state.setActiveTab("code")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  state.activeTab === "code"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>💻</span>
                Code
              </button>
              <button
                onClick={() => state.setActiveTab("planning")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  state.activeTab === "planning"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>📝</span>
                Planning
              </button>
              <button
                onClick={() => state.setActiveTab("graph")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  state.activeTab === "graph"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>🕸️</span>
                Graph
              </button>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 bg-[#0a0a0a] overflow-hidden">
            {state.activeTab === "preview" ? (
              // Show Globe component until actual preview is generated
              <div className="h-full w-full bg-[#0a0a0a] overflow-hidden relative">
                <Globe />
              </div>
            ) : state.activeTab === "code" ? (
              <div className="h-full flex">
                {/* File Explorer */}
                <div className="w-64 bg-[#0a0a0a] border-r border-[#1a1a1a] flex flex-col">
                  <div className="px-4 py-3 border-b border-[#1a1a1a] flex items-center justify-between">
                    <h3 className="text-sm font-medium text-white">Explorer</h3>
                  </div>
                  <div className="flex-1 p-4 overflow-y-auto">
                    <div className="text-xs text-[#666] space-y-2">
                      <div className="p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer transition-colors">📁 src</div>
                      <div className="p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer ml-4 transition-colors">📄 app.tsx</div>
                      <div className="p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer ml-4 transition-colors">📄 index.css</div>
                      <div className="p-2 hover:bg-[#1a1a1a] rounded-lg cursor-pointer ml-4 transition-colors">📄 components.tsx</div>
                    </div>
                  </div>
                </div>

                {/* Code Editor */}
                <div className="flex-1 bg-[#0a0a0a] p-4">
                  <div className="h-full flex items-center justify-center bg-[#111111] rounded-lg border border-[#1a1a1a]">
                    <div className="text-center text-[#666] p-8">
                      <div className="w-12 h-12 mx-auto mb-4 opacity-50">💻</div>
                      <p className="text-sm">Code editor will appear here</p>
                      <p className="text-xs mt-2 opacity-70">Select a file to start editing</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : state.activeTab === "planning" ? (
              // Planning Tab - Show detailed planning results
              <div className="h-full overflow-hidden">
                {!state.hasStarted ? (
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center text-gray-400">
                      <div className="text-sm">Start planning to see your project breakdown</div>
                    </div>
                  </div>
                ) : !state.isProcessing && Object.keys(state.results).length > 0 ? (
                  // Show detailed planning results when complete
                  <div className="h-full flex overflow-hidden">
                    {/* Left Sidebar - Navigation */}
                    <div className="w-64 bg-[#0a0a0a] border-r border-[#1a1a1a] overflow-y-auto flex-shrink-0">
                      <div className="p-4 border-b border-[#1a1a1a]">
                        <div className="flex items-center gap-2">
                          <h2 className="text-white font-medium">Project Plan</h2>
                          <button className="ml-auto text-gray-400 hover:text-white text-sm hover:bg-[#1a1a1a] px-2 py-1 rounded transition-colors">
                            Export
                          </button>
                        </div>
                      </div>

                      <div className="p-2">
                        {Object.keys(state.results).map((key) => {
                          const isSelected = state.selectedPlanningSection === key
                          const sectionName = key.replace(/([A-Z])/g, ' $1').trim()

                          return (
                            <button
                              key={key}
                              onClick={() => state.setSelectedPlanningSection(key)}
                              className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                                isSelected
                                  ? 'bg-[#ff2d55] text-white'
                                  : 'hover:bg-[#1a1a1a] text-gray-300'
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <div className={`w-2 h-2 rounded-full ${
                                  isSelected ? 'bg-white' : 'bg-green-500'
                                }`}></div>
                                <span className="text-sm font-medium capitalize">
                                  {sectionName}
                                </span>
                              </div>
                            </button>
                          )
                        })}
                      </div>
                    </div>

                    {/* Right Content - Selected Section Details */}
                    <div className="flex-1 bg-[#111111] overflow-y-auto max-h-full">
                      {state.selectedPlanningSection ? (
                        <div className="p-4">
                          <h2 className="text-lg font-semibold text-white mb-4 capitalize">
                            {state.selectedPlanningSection.replace(/([A-Z])/g, ' $1').trim()}
                          </h2>
                          <div className="bg-gray-900/50 rounded-lg p-6">
                            <pre className="whitespace-pre-wrap text-gray-300 text-sm">
                              {JSON.stringify(state.results[state.selectedPlanningSection], null, 2)}
                            </pre>
                          </div>
                        </div>
                      ) : (
                        <div className="h-full flex items-center justify-center">
                          <div className="text-center text-gray-400">
                            <div className="text-lg mb-2">📋</div>
                            <div className="text-sm">Select a section to view details</div>
                            <div className="text-xs mt-2 opacity-70">Click on any section in the sidebar to see its content</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  // Show placeholder during planning or if no results
                  <div className="h-full flex items-center justify-center p-6">
                    <div className="text-center text-gray-400">
                      <div className="text-sm">
                        {state.isProcessing ? 'Planning in progress...' : 'Planning details will appear here'}
                      </div>
                      <div className="text-xs mt-2 opacity-70">
                        {state.isProcessing ? 'Check the chat for live progress' : 'Detailed planning results will be shown in this tab'}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : state.activeTab === "graph" ? (
              // Graph Tab - Neo4j Visualization
              <div className="h-full overflow-hidden">
                <div className="h-full flex items-center justify-center p-6">
                  <div className="text-center text-gray-400 max-w-2xl">
                    <div className="w-16 h-16 mx-auto mb-6 opacity-50">🕸️</div>
                    <h3 className="text-lg font-medium text-white mb-4">Project Knowledge Graph</h3>
                    <div className="text-sm mb-6">
                      Interactive visualization of your project's context and relationships powered by Neo4j and Graphiti.
                    </div>
                    <div className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-6 text-left">
                      <h4 className="text-white font-medium mb-3">🚧 Coming Soon</h4>
                      <div className="space-y-2 text-sm">
                        <p>• Real-time Neo4j graph visualization using Zep Graph Visualization</p>
                        <p>• Interactive exploration of project entities and relationships</p>
                        <p>• Context-aware insights from your project's knowledge graph</p>
                        <p>• Dynamic updates as your project evolves</p>
                      </div>
                      <div className="mt-4 pt-4 border-t border-[#1a1a1a]">
                        <p className="text-xs text-gray-500">
                          This visualization will integrate with our Context Engine to provide deep insights into your project's structure and dependencies.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
          </div>
        </main>
      </div>
    </div>
  )
}
